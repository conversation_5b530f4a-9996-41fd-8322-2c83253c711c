<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minimal Camera Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f0f0;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        video {
            width: 100%;
            max-width: 640px;
            height: auto;
            border: 2px solid #007bff;
            border-radius: 8px;
            background: #000;
        }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px 5px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        
        .status {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎥 Minimal Camera Test</h1>
        <p>This is a basic test to check if camera access works in your browser.</p>
        
        <div class="controls">
            <button id="startBtn">Start Camera</button>
            <button id="stopBtn" disabled>Stop Camera</button>
            <button id="infoBtn">Browser Info</button>
        </div>
        
        <video id="video" autoplay muted playsinline></video>
        
        <div id="status" class="status">Ready to test camera...</div>
    </div>

    <script>
        const video = document.getElementById('video');
        const startBtn = document.getElementById('startBtn');
        const stopBtn = document.getElementById('stopBtn');
        const infoBtn = document.getElementById('infoBtn');
        const status = document.getElementById('status');
        
        let currentStream = null;
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${message}\n`;
            
            status.textContent += logMessage;
            status.scrollTop = status.scrollHeight;
            
            if (type === 'error') {
                status.className = 'status error';
            } else if (type === 'success') {
                status.className = 'status success';
            } else {
                status.className = 'status';
            }
            
            console.log(message);
        }
        
        function clearLog() {
            status.textContent = '';
            status.className = 'status';
        }
        
        async function startCamera() {
            clearLog();
            log('🚀 Starting camera test...');
            
            try {
                // Check browser support
                if (!navigator.mediaDevices) {
                    throw new Error('navigator.mediaDevices not supported');
                }
                
                if (!navigator.mediaDevices.getUserMedia) {
                    throw new Error('getUserMedia not supported');
                }
                
                log('✅ Browser supports getUserMedia');
                log('🌐 Protocol: ' + window.location.protocol);
                log('🏠 Host: ' + window.location.host);
                
                // Request camera access
                log('📱 Requesting camera access...');
                
                const constraints = {
                    video: {
                        width: { ideal: 640 },
                        height: { ideal: 480 }
                    }
                };
                
                log('🎥 Constraints: ' + JSON.stringify(constraints));
                
                currentStream = await navigator.mediaDevices.getUserMedia(constraints);
                log('✅ Camera stream obtained!', 'success');
                
                // Assign to video element
                video.srcObject = currentStream;
                log('📺 Stream assigned to video element');
                
                // Wait for video to load
                video.onloadedmetadata = () => {
                    log('📊 Video metadata loaded');
                    log(`📐 Video size: ${video.videoWidth}x${video.videoHeight}`);
                };
                
                video.onplay = () => {
                    log('▶️ Video started playing', 'success');
                };
                
                video.onerror = (error) => {
                    log('❌ Video error: ' + error, 'error');
                };
                
                // Update button states
                startBtn.disabled = true;
                stopBtn.disabled = false;
                
            } catch (error) {
                log('❌ Camera error: ' + error.name + ' - ' + error.message, 'error');
                
                if (error.name === 'NotAllowedError') {
                    log('🚫 Camera access denied by user', 'error');
                    log('💡 Please click "Allow" when prompted for camera access');
                } else if (error.name === 'NotFoundError') {
                    log('📷 No camera found on this device', 'error');
                } else if (error.name === 'NotReadableError') {
                    log('🔒 Camera is already in use by another application', 'error');
                } else if (error.name === 'OverconstrainedError') {
                    log('⚙️ Camera constraints cannot be satisfied', 'error');
                } else if (error.name === 'SecurityError') {
                    log('🔐 Security error - HTTPS may be required', 'error');
                }
            }
        }
        
        function stopCamera() {
            log('⏹️ Stopping camera...');
            
            if (currentStream) {
                const tracks = currentStream.getTracks();
                tracks.forEach(track => {
                    track.stop();
                    log(`🛑 Stopped ${track.kind} track`);
                });
                currentStream = null;
                video.srcObject = null;
                log('✅ Camera stopped', 'success');
            } else {
                log('ℹ️ No active camera stream');
            }
            
            // Update button states
            startBtn.disabled = false;
            stopBtn.disabled = true;
        }
        
        function showBrowserInfo() {
            clearLog();
            log('🌐 Browser Information:');
            log('User Agent: ' + navigator.userAgent);
            log('Platform: ' + navigator.platform);
            log('Language: ' + navigator.language);
            log('Online: ' + navigator.onLine);
            log('');
            log('📱 Media Devices Support:');
            log('navigator.mediaDevices: ' + (navigator.mediaDevices ? 'YES' : 'NO'));
            log('getUserMedia: ' + (navigator.mediaDevices?.getUserMedia ? 'YES' : 'NO'));
            log('');
            log('🔒 Security Context:');
            log('Protocol: ' + window.location.protocol);
            log('Secure Context: ' + window.isSecureContext);
            log('');
            
            if (navigator.mediaDevices && navigator.mediaDevices.enumerateDevices) {
                navigator.mediaDevices.enumerateDevices()
                    .then(devices => {
                        log('📹 Available Devices:');
                        devices.forEach((device, index) => {
                            log(`${index + 1}. ${device.kind}: ${device.label || 'Unknown'}`);
                        });
                    })
                    .catch(err => {
                        log('❌ Could not enumerate devices: ' + err.message);
                    });
            }
        }
        
        // Event listeners
        startBtn.addEventListener('click', startCamera);
        stopBtn.addEventListener('click', stopCamera);
        infoBtn.addEventListener('click', showBrowserInfo);
        
        // Auto-show browser info on load
        window.onload = () => {
            log('🚀 Minimal camera test loaded');
            showBrowserInfo();
        };
    </script>
</body>
</html>
