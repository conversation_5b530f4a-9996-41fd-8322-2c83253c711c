body {
    font-family: Arial, sans-serif;
    background-color: #121212;
    color: #EAEAEA;
    margin: 0;
    padding: 0;
}

.container {
    max-width: 600px;
    margin: auto;
    padding: 20px;
    background-color: #1E1E1E;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
}

.profile-header {
    text-align: center;
    margin-bottom: 20px;
}

.profile-header h1 {
    color: #EAEAEA;
}

.profile-image img {
    border-radius: 50%;
    width: 150px;
    height: 150px;
}

.profile-details {
    margin: 20px 0;
}

.profile-details h2, 
.profile-details p {
    margin: 10px 0;
    color: #D1C6E2; /* Violet shade */
}

.actions {
    text-align: center;
}

.actions button {
    background-color: #6A0DAD; /* Violet */
    color: #EAEAEA;
    border: none;
    border-radius: 5px;
    padding: 10px 20px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.actions button:hover {
    background-color: #5E0B99; /* Darker violet */
}

.actions button i {
    margin-right: 5px;
}
