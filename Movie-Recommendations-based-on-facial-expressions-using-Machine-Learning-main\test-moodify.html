<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MoodifyMe - Facial Detection Test</title>
    <script defer src="face-api.min.js"></script>
    <style>
        body {
            background-color: #1a1a1a;
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            color: white;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            width: 100%;
            text-align: center;
        }
        
        h1 {
            color: #E55100;
            margin-bottom: 30px;
        }
        
        .video-container {
            position: relative;
            display: inline-block;
            margin: 20px 0;
        }
        
        video {
            width: 640px;
            height: 480px;
            border-radius: 10px;
            border: 2px solid #E55100;
        }
        
        canvas {
            position: absolute;
            top: 0;
            left: 0;
            border-radius: 10px;
        }
        
        .controls {
            margin: 20px 0;
        }
        
        button {
            background: #E55100;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            margin: 0 10px;
            transition: background 0.3s;
        }
        
        button:hover {
            background: #D32F2F;
        }
        
        button:disabled {
            background: #666;
            cursor: not-allowed;
        }
        
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 6px;
            background: #333;
        }
        
        .emotion-display {
            font-size: 24px;
            font-weight: bold;
            color: #FFD54F;
            margin: 10px 0;
        }
        
        .confidence {
            font-size: 18px;
            color: #81C784;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎭 MoodifyMe - Facial Detection Test</h1>
        <p>Testing facial landmark detection in the working environment</p>
        
        <div class="video-container">
            <video id="video" autoplay muted></video>
        </div>
        
        <div class="controls">
            <button id="startBtn">Start Camera</button>
            <button id="captureBtn" disabled>Capture Emotion</button>
        </div>
        
        <div class="status" id="status">
            Click "Start Camera" to begin facial detection
        </div>
        
        <div class="emotion-display" id="emotionDisplay"></div>
        <div class="confidence" id="confidenceDisplay"></div>
    </div>

    <script>
        const video = document.getElementById('video');
        const startBtn = document.getElementById('startBtn');
        const captureBtn = document.getElementById('captureBtn');
        const status = document.getElementById('status');
        const emotionDisplay = document.getElementById('emotionDisplay');
        const confidenceDisplay = document.getElementById('confidenceDisplay');
        
        let currentEmotion = '';
        let isDetecting = false;

        // Load face-api models
        async function loadModels() {
            try {
                status.innerHTML = '🔄 Loading facial detection models...';
                
                await Promise.all([
                    faceapi.nets.tinyFaceDetector.loadFromUri('./models'),
                    faceapi.nets.faceLandmark68Net.loadFromUri('./models'),
                    faceapi.nets.faceRecognitionNet.loadFromUri('./models'),
                    faceapi.nets.faceExpressionNet.loadFromUri('./models')
                ]);
                
                status.innerHTML = '✅ Models loaded successfully!';
                console.log("Models loaded successfully");
                return true;
            } catch (error) {
                status.innerHTML = '❌ Error loading models: ' + error.message;
                console.error("Model loading failed:", error);
                return false;
            }
        }

        // Start video
        async function startVideo() {
            try {
                status.innerHTML = '📹 Starting camera...';
                
                const stream = await navigator.mediaDevices.getUserMedia({ video: {} });
                video.srcObject = stream;
                
                status.innerHTML = '✅ Camera started! Initializing detection...';
                startBtn.disabled = true;
                captureBtn.disabled = false;
                
                return true;
            } catch (error) {
                status.innerHTML = '❌ Error accessing camera: ' + error.message;
                console.error('Error accessing webcam:', error);
                return false;
            }
        }

        // Start detection when video is ready
        video.addEventListener('loadedmetadata', () => {
            const canvas = faceapi.createCanvasFromMedia(video);
            document.querySelector('.video-container').appendChild(canvas);

            const displaySize = { width: video.videoWidth, height: video.videoHeight };
            faceapi.matchDimensions(canvas, displaySize);

            canvas.width = displaySize.width;
            canvas.height = displaySize.height;

            // Start detection loop - exactly like working project
            setInterval(async () => {
                if (!isDetecting) return;
                
                const detections = await faceapi.detectAllFaces(video, new faceapi.TinyFaceDetectorOptions())
                    .withFaceLandmarks()
                    .withFaceExpressions();

                const resizedDetections = faceapi.resizeResults(detections, displaySize);
                const ctx = canvas.getContext('2d');
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                if (detections.length > 0) {
                    console.log("Face detected");
                    
                    // Draw landmarks exactly like working project
                    faceapi.draw.drawFaceLandmarks(canvas, resizedDetections);
                    faceapi.draw.drawFaceExpressions(canvas, resizedDetections);

                    currentEmotion = getDominantEmotion(detections[0].expressions);
                    const confidence = Math.max(...Object.values(detections[0].expressions));
                    
                    emotionDisplay.innerHTML = `Current Emotion: ${currentEmotion}`;
                    confidenceDisplay.innerHTML = `Confidence: ${Math.round(confidence * 100)}%`;
                    
                    console.log("Current emotion:", currentEmotion);
                } else {
                    console.log("No face detected");
                    emotionDisplay.innerHTML = 'No face detected';
                    confidenceDisplay.innerHTML = '';
                }
            }, 100);
        });

        // Get dominant emotion - exactly like working project
        function getDominantEmotion(expressions) {
            return Object.keys(expressions).reduce((a, b) => expressions[a] > expressions[b] ? a : b);
        }

        // Event listeners
        startBtn.addEventListener('click', async () => {
            const modelsLoaded = await loadModels();
            if (modelsLoaded) {
                const videoStarted = await startVideo();
                if (videoStarted) {
                    isDetecting = true;
                    status.innerHTML = '🎯 Facial detection active! Position your face in the camera.';
                }
            }
        });

        captureBtn.addEventListener('click', () => {
            if (currentEmotion) {
                alert(`Captured Emotion: ${currentEmotion}\n\nThis would now redirect to MoodifyMe mood options!`);
            } else {
                alert('No emotion detected yet. Please make sure your face is visible.');
            }
        });
    </script>
</body>
</html>
