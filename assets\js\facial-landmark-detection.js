/**
 * MoodifyMe - Facial Landmark Detection
 * Uses face-api.js to detect facial landmarks and emotions
 */

// Models to load
const MODELS_PATH = 'assets/models';
let modelsLoaded = false;
let isProcessing = false;

// DOM elements
let video;
let canvas;
let captureButton;
let landmarksOverlay;
let emotionResult;
let loadingIndicator;

// Initialize facial detection
async function initFacialDetection() {
    try {
        loadingIndicator = document.getElementById('facial-loading');
        video = document.getElementById('face-video');
        canvas = document.getElementById('face-canvas');
        captureButton = document.getElementById('capture-face');
        emotionResult = document.getElementById('face-result');

        // Create landmarks overlay if it doesn't exist
        landmarksOverlay = document.getElementById('landmarks-overlay');
        if (!landmarksOverlay && video) {
            landmarksOverlay = document.createElement('canvas');
            landmarksOverlay.id = 'landmarks-overlay';
            landmarksOverlay.style.position = 'absolute';
            landmarksOverlay.style.top = '0';
            landmarksOverlay.style.left = '0';
            landmarksOverlay.style.pointerEvents = 'none';
            landmarksOverlay.style.zIndex = '10';
            video.parentNode.style.position = 'relative';
            video.parentNode.appendChild(landmarksOverlay);
        }

        if (!video || !canvas || !captureButton) {
            console.error('Required DOM elements not found');
            return;
        }

        // Show loading indicator
        if (loadingIndicator) {
            loadingIndicator.style.display = 'block';
        }

        // Load models
        await loadModels();

        // Hide loading indicator
        if (loadingIndicator) {
            loadingIndicator.style.display = 'none';
        }

        // Add event listeners
        captureButton.addEventListener('click', captureAndAnalyze);

        // Start real-time landmark detection when video is playing
        video.addEventListener('play', () => {
            // Create canvas for landmarks overlay
            landmarksOverlay.width = video.width;
            landmarksOverlay.height = video.height;

            // Start detection loop
            detectFacialLandmarks();
        });

        console.log('Facial detection initialized successfully');
    } catch (error) {
        console.error('Error initializing facial detection:', error);
        if (loadingIndicator) {
            loadingIndicator.style.display = 'none';
        }

        // Show error message
        if (emotionResult) {
            emotionResult.innerHTML = `
                <div class="alert alert-danger">
                    <p><i class="fas fa-exclamation-triangle"></i> Error initializing facial detection.</p>
                    <p>Please make sure you have a working camera and try again.</p>
                </div>
            `;
        }
    }
}

// Load required face-api.js models with enhanced error handling
async function loadModels() {
    if (modelsLoaded) return;

    try {
        console.log('Loading face-api.js models...');

        // Load all models in parallel for better performance
        await Promise.all([
            faceapi.nets.tinyFaceDetector.loadFromUri(MODELS_PATH),
            faceapi.nets.faceLandmark68Net.loadFromUri(MODELS_PATH),
            faceapi.nets.faceRecognitionNet.loadFromUri(MODELS_PATH),
            faceapi.nets.faceExpressionNet.loadFromUri(MODELS_PATH)
        ]);

        modelsLoaded = true;
        console.log('All face-api.js models loaded successfully');

        // Show success message
        if (emotionResult) {
            emotionResult.innerHTML = `
                <div class="alert alert-success">
                    <p><i class="fas fa-check-circle"></i> Facial detection models loaded successfully!</p>
                    <p>You can now use advanced facial emotion recognition.</p>
                </div>
            `;
        }

    } catch (error) {
        console.error('Error loading face-api.js models:', error);

        // Try fallback approach with individual model loading
        try {
            console.log('Attempting fallback model loading...');

            // Load essential models only
            await faceapi.nets.tinyFaceDetector.loadFromUri(MODELS_PATH);
            await faceapi.nets.faceLandmark68Net.loadFromUri(MODELS_PATH);
            await faceapi.nets.faceExpressionNet.loadFromUri(MODELS_PATH);

            modelsLoaded = true;
            console.log('Essential models loaded successfully (fallback mode)');

            if (emotionResult) {
                emotionResult.innerHTML = `
                    <div class="alert alert-warning">
                        <p><i class="fas fa-info-circle"></i> Basic facial detection models loaded.</p>
                        <p>Some advanced features may not be available.</p>
                    </div>
                `;
            }

        } catch (fallbackError) {
            console.error('Fallback model loading also failed:', fallbackError);

            // Show error in UI
            if (emotionResult) {
                emotionResult.innerHTML = `
                    <div class="alert alert-danger">
                        <p><i class="fas fa-exclamation-triangle"></i> Error loading facial detection models.</p>
                        <p>Please check your internet connection and try again, or use text input instead.</p>
                        <small>Error: ${fallbackError.message}</small>
                    </div>
                `;
            }

            throw fallbackError;
        }
    }
}

// Detect facial landmarks in real-time
async function detectFacialLandmarks() {
    if (!modelsLoaded || !video || !landmarksOverlay || video.paused || video.ended || isProcessing) {
        return;
    }

    isProcessing = true;

    try {
        // Get video dimensions
        const videoWidth = video.videoWidth;
        const videoHeight = video.videoHeight;

        // Resize overlay to match video dimensions
        landmarksOverlay.width = videoWidth;
        landmarksOverlay.height = videoHeight;

        // Detect all faces with landmarks and expressions
        // Try SSD MobileNet first, fall back to Tiny Face Detector if needed
        let detections;
        try {
            detections = await faceapi.detectAllFaces(video)
                .withFaceLandmarks()
                .withFaceExpressions();
        } catch (e) {
            // Fallback to Tiny Face Detector
            detections = await faceapi.detectAllFaces(video, new faceapi.TinyFaceDetectorOptions())
                .withFaceLandmarks()
                .withFaceExpressions();
        }

        // Draw results on overlay
        const ctx = landmarksOverlay.getContext('2d');
        ctx.clearRect(0, 0, videoWidth, videoHeight);

        if (detections.length > 0) {
            // Enhanced landmark visualization using face-api.js built-in drawing functions
            detections.forEach(detection => {
                const { landmarks, expressions, detection: { box } } = detection;

                // Resize detections to match canvas dimensions
                const resizedDetections = faceapi.resizeResults([detection], { width: videoWidth, height: videoHeight });

                // Draw face bounding box with enhanced styling
                ctx.strokeStyle = '#FF00FF'; // Magenta like in your image
                ctx.lineWidth = 3;
                ctx.strokeRect(box.x, box.y, box.width, box.height);

                // Use face-api.js built-in landmark drawing for cleaner lines
                ctx.strokeStyle = '#FF00FF'; // Magenta color like in your image
                ctx.lineWidth = 2;

                // Draw facial landmarks using the built-in function for better connectivity
                faceapi.draw.drawFaceLandmarks(landmarksOverlay, resizedDetections, {
                    lineWidth: 2,
                    color: '#FF00FF' // Magenta color
                });

                // Add custom enhanced landmark visualization
                drawEnhancedLandmarks(ctx, landmarks.positions, box);

                // Get dominant expression
                const dominantExpression = getDominantExpression(expressions);

                // Draw expression label with enhanced visibility
                ctx.font = 'bold 18px Arial';
                ctx.fillStyle = '#FFFFFF';
                ctx.strokeStyle = '#000000';
                ctx.lineWidth = 3;

                const text = `${dominantExpression.expression} (${(dominantExpression.probability * 100).toFixed(1)}%)`;
                const textWidth = ctx.measureText(text).width;

                // Position text below face for better visibility
                const x = box.x + (box.width / 2) - (textWidth / 2);
                const y = box.y + box.height + 30;

                // Draw text with outline for better visibility
                ctx.strokeText(text, x, y);
                ctx.fillText(text, x, y);

                // Update real-time emotion display
                updateRealTimeEmotionDisplay(dominantExpression.expression, dominantExpression.probability, expressions);

                // Add emotion to history for smoothing
                emotionHistory.push({
                    emotion: dominantExpression.expression,
                    confidence: dominantExpression.probability,
                    timestamp: Date.now()
                });

                // Keep only recent history (last 10 entries)
                if (emotionHistory.length > 10) {
                    emotionHistory.shift();
                }
            });
        }
    } catch (error) {
        console.error('Error detecting facial landmarks:', error);
    } finally {
        isProcessing = false;

        // Continue detection loop
        requestAnimationFrame(detectFacialLandmarks);
    }
}

// Capture image and analyze emotions
async function captureAndAnalyze() {
    if (!modelsLoaded || !video || !canvas) {
        console.error('Models not loaded or required elements not found');
        return;
    }

    try {
        // Show loading indicator
        if (loadingIndicator) {
            loadingIndicator.style.display = 'block';
        }

        // Draw video frame to canvas
        const context = canvas.getContext('2d');
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        context.drawImage(video, 0, 0, canvas.width, canvas.height);

        // Detect face with landmarks and expressions
        // Try SSD MobileNet first, fall back to Tiny Face Detector if needed
        let detections;
        try {
            detections = await faceapi.detectAllFaces(canvas)
                .withFaceLandmarks()
                .withFaceExpressions();
        } catch (e) {
            // Fallback to Tiny Face Detector
            detections = await faceapi.detectAllFaces(canvas, new faceapi.TinyFaceDetectorOptions())
                .withFaceLandmarks()
                .withFaceExpressions();
        }

        if (detections.length === 0) {
            throw new Error('No face detected');
        }

        // Get the first detected face (assuming single user)
        const detection = detections[0];
        const { landmarks, expressions, detection: { box } } = detection;
        const points = landmarks.positions;

        // Resize detections to match canvas dimensions
        const resizedDetections = faceapi.resizeResults([detection], { width: canvas.width, height: canvas.height });

        // Draw face bounding box with enhanced styling
        context.strokeStyle = '#FF00FF'; // Magenta like in your image
        context.lineWidth = 3;
        context.strokeRect(box.x, box.y, box.width, box.height);

        // Use face-api.js built-in landmark drawing for cleaner lines
        faceapi.draw.drawFaceLandmarks(canvas, resizedDetections, {
            lineWidth: 2,
            color: '#FF00FF' // Magenta color
        });

        // Add custom enhanced landmark visualization
        drawEnhancedLandmarks(context, points, box);

        // Get dominant expression
        const dominantExpression = getDominantExpression(expressions);

        // Map face-api expression to our emotion categories
        const emotion = mapExpressionToEmotion(dominantExpression.expression);
        const confidence = dominantExpression.probability;

        // Draw emotion label with enhanced styling
        context.font = 'bold 20px Arial';
        context.fillStyle = '#FFFFFF';
        context.strokeStyle = '#000000';
        context.lineWidth = 3;

        const text = `${emotion} (${(confidence * 100).toFixed(1)}%)`;
        const textWidth = context.measureText(text).width;

        // Position text below face for better visibility
        const x = box.x + (box.width / 2) - (textWidth / 2);
        const y = box.y + box.height + 35;

        // Draw text with outline for better visibility
        context.strokeText(text, x, y);
        context.fillText(text, x, y);

        // Draw confidence indicator
        drawConfidenceIndicator(context, emotion, confidence, box);

        // Convert canvas to blob for server upload
        canvas.toBlob(async function(blob) {
            // Create form data to send to server
            const formData = new FormData();
            formData.append('input_type', 'face');
            formData.append('image_data', blob);
            formData.append('detected_emotion', emotion);
            formData.append('confidence', confidence);

            // Send to server
            try {
                // Determine the correct API URL based on the current page location
                const apiUrl = window.location.pathname.includes('/pages/') ?
                    '../api/emotion_analysis.php' : 'api/emotion_analysis.php';

                // Attempt to send data to server
                let fetchOptions = {
                    method: 'POST',
                    body: formData
                };

                // Add timeout if supported
                try {
                    fetchOptions.signal = AbortSignal.timeout(10000); // 10 second timeout
                } catch (e) {
                    console.warn('AbortSignal.timeout not supported, proceeding without timeout');
                }

                const response = await fetch(apiUrl, fetchOptions);

                const data = await response.json();

                if (data.success) {
                    // Call the showEmotionResults function from emotion-detection.js
                    if (typeof window.showEmotionResults === 'function') {
                        window.showEmotionResults(data.emotion, data.confidence);
                    } else {
                        // Fallback if the function isn't available
                        if (emotionResult) {
                            emotionResult.innerHTML = `
                                <div class="alert alert-success">
                                    <p>Detected emotion: <strong>${data.emotion}</strong> (${Math.round(data.confidence * 100)}% confidence)</p>
                                    <p>Please continue to select your target emotion.</p>
                                </div>
                            `;
                        }
                    }

                    // Stop camera stream
                    if (video.srcObject) {
                        video.srcObject.getTracks().forEach(track => track.stop());
                        video.srcObject = null;
                    }
                } else {
                    throw new Error(data.message || 'Error processing emotion');
                }
            } catch (error) {
                console.error('Error sending data to server:', error);

                // Check if it's a network error or timeout
                if (error.name === 'AbortError' || error.name === 'TypeError' || error.message.includes('NetworkError')) {
                    console.log('Network error detected, using local fallback');

                    // Use the locally detected emotion as a fallback
                    if (typeof window.showEmotionResults === 'function') {
                        window.showEmotionResults(emotion, confidence);

                        // Show a notice that we're using local detection
                        if (emotionResult) {
                            emotionResult.innerHTML += `
                                <div class="alert alert-warning mt-3">
                                    <p><i class="fas fa-info-circle"></i> Using locally detected emotion due to server connection issues.</p>
                                </div>
                            `;
                        }

                        // Stop camera stream
                        if (video.srcObject) {
                            video.srcObject.getTracks().forEach(track => track.stop());
                            video.srcObject = null;
                        }
                    } else {
                        showError('Network error: Could not connect to the server. Please check your internet connection and try again.');
                    }
                } else {
                    showError('Error processing your facial expression. Please try again.');
                }
            } finally {
                // Hide loading indicator
                if (loadingIndicator) {
                    loadingIndicator.style.display = 'none';
                }
            }
        });
    } catch (error) {
        console.error('Error capturing and analyzing:', error);

        // Hide loading indicator
        if (loadingIndicator) {
            loadingIndicator.style.display = 'none';
        }

        // Show error message
        showError(error.message === 'No face detected' ?
            'No face detected. Please make sure your face is clearly visible.' :
            'Error analyzing facial expression. Please try again.');
    }
}

// Get the dominant expression from expressions object
function getDominantExpression(expressions) {
    let dominantExpression = '';
    let maxProbability = 0;

    for (const [expression, probability] of Object.entries(expressions)) {
        if (probability > maxProbability) {
            dominantExpression = expression;
            maxProbability = probability;
        }
    }

    return {
        expression: dominantExpression,
        probability: maxProbability
    };
}

// Map face-api expression to our emotion categories
function mapExpressionToEmotion(expression) {
    const mapping = {
        'neutral': 'neutral',
        'happy': 'happy',
        'sad': 'sad',
        'angry': 'angry',
        'fearful': 'anxious',
        'disgusted': 'stressed',
        'surprised': 'excited'
    };

    return mapping[expression] || 'neutral';
}

// Enhanced landmark drawing function with better visual styling
function drawEnhancedLandmarks(ctx, points, box) {
    // Define colors for different facial features (matching your image style)
    const colors = {
        jawOutline: '#00FFFF',    // Cyan for jaw
        leftEyeBrow: '#FF00FF',   // Magenta for eyebrows
        rightEyeBrow: '#FF00FF',  // Magenta for eyebrows
        nose: '#00FF00',          // Green for nose
        leftEye: '#FFFF00',       // Yellow for eyes
        rightEye: '#FFFF00',      // Yellow for eyes
        mouth: '#FF0000'          // Red for mouth
    };

    // Draw enhanced landmark points with better visibility
    const pointSize = 2;
    const lineWidth = 2;

    // Jaw outline (points 0-16) - Connected line
    drawLandmarkRegion(ctx, points.slice(0, 17), colors.jawOutline, pointSize, lineWidth, false);

    // Left eyebrow (points 17-21)
    drawLandmarkRegion(ctx, points.slice(17, 22), colors.leftEyeBrow, pointSize, lineWidth, false);

    // Right eyebrow (points 22-26)
    drawLandmarkRegion(ctx, points.slice(22, 27), colors.rightEyeBrow, pointSize, lineWidth, false);

    // Nose bridge and tip (points 27-35)
    drawLandmarkRegion(ctx, points.slice(27, 36), colors.nose, pointSize, lineWidth, false);

    // Left eye (points 36-41) - Closed loop
    drawLandmarkRegion(ctx, points.slice(36, 42), colors.leftEye, pointSize, lineWidth, true);

    // Right eye (points 42-47) - Closed loop
    drawLandmarkRegion(ctx, points.slice(42, 48), colors.rightEye, pointSize, lineWidth, true);

    // Outer mouth (points 48-59) - Closed loop
    drawLandmarkRegion(ctx, points.slice(48, 60), colors.mouth, pointSize, lineWidth, true);

    // Inner mouth (points 60-67) - Closed loop
    if (points.length > 67) {
        drawLandmarkRegion(ctx, points.slice(60, 68), colors.mouth, pointSize, lineWidth, true);
    }
}

// Draw landmark points with specified color and size
function drawLandmarkRegion(ctx, points, color, pointSize, lineWidth, closePath = false) {
    if (points.length === 0) return;

    // Draw points
    ctx.fillStyle = color;
    points.forEach(point => {
        ctx.beginPath();
        ctx.arc(point.x, point.y, pointSize, 0, 2 * Math.PI);
        ctx.fill();
    });

    // Draw connecting lines
    if (points.length > 1) {
        ctx.strokeStyle = color;
        ctx.lineWidth = lineWidth;
        ctx.beginPath();
        ctx.moveTo(points[0].x, points[0].y);

        for (let i = 1; i < points.length; i++) {
            ctx.lineTo(points[i].x, points[i].y);
        }

        // Close the path if specified (for eyes and mouth)
        if (closePath) {
            ctx.closePath();
        }

        ctx.stroke();
    }
}

// Legacy function for backward compatibility
function drawLandmarkPoints(ctx, points, color, size) {
    drawLandmarkRegion(ctx, points, color, size, 2, false);
}

// Draw emotion probability bars
function drawEmotionProbabilityBars(ctx, expressions, box) {
    const emotions = [
        { name: 'happy', color: 'rgba(0, 255, 0, 0.7)' },
        { name: 'sad', color: 'rgba(0, 0, 255, 0.7)' },
        { name: 'angry', color: 'rgba(255, 0, 0, 0.7)' },
        { name: 'fearful', color: 'rgba(255, 255, 0, 0.7)' },
        { name: 'disgusted', color: 'rgba(128, 0, 128, 0.7)' },
        { name: 'surprised', color: 'rgba(0, 255, 255, 0.7)' },
        { name: 'neutral', color: 'rgba(128, 128, 128, 0.7)' }
    ];

    const barWidth = 100;
    const barHeight = 10;
    const barSpacing = 15;
    const startX = box.x + box.width + 10;
    const startY = box.y;

    // Draw title for emotion probabilities
    ctx.font = 'bold 14px Arial';
    ctx.fillStyle = 'white';
    ctx.strokeStyle = 'black';
    ctx.lineWidth = 1;
    ctx.strokeText('Emotion Probabilities:', startX - 60, startY - 10);
    ctx.fillText('Emotion Probabilities:', startX - 60, startY - 10);

    // Draw emotion bars
    emotions.forEach((emotion, index) => {
        const probability = expressions[emotion.name] || 0;
        const y = startY + (index * barSpacing);

        // Draw bar background
        ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
        ctx.fillRect(startX, y, barWidth, barHeight);

        // Draw filled portion based on probability
        ctx.fillStyle = emotion.color;
        ctx.fillRect(startX, y, barWidth * probability, barHeight);

        // Draw label
        ctx.font = '12px Arial';
        ctx.fillStyle = 'white';
        ctx.strokeStyle = 'black';
        ctx.lineWidth = 1;
        ctx.strokeText(`${emotion.name}`, startX - 60, y + 9);
        ctx.fillText(`${emotion.name}`, startX - 60, y + 9);

        // Draw percentage
        const percentText = `${Math.round(probability * 100)}%`;
        ctx.strokeText(percentText, startX + barWidth + 5, y + 9);
        ctx.fillText(percentText, startX + barWidth + 5, y + 9);
    });
}

// Draw confidence level indicator
function drawConfidenceIndicator(ctx, emotion, confidence, box) {
    // Position below the face
    const centerX = box.x + (box.width / 2);
    const y = box.y + box.height + 30;
    const width = box.width * 1.2;
    const height = 30;
    const startX = centerX - (width / 2);

    // Draw background
    ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    ctx.beginPath();
    ctx.roundRect(startX, y, width, height, 5);
    ctx.fill();

    // Draw confidence bar
    const confidenceWidth = width * confidence;

    // Choose color based on confidence level
    let barColor;
    if (confidence < 0.4) {
        barColor = 'rgba(255, 0, 0, 0.8)'; // Red for low confidence
    } else if (confidence < 0.7) {
        barColor = 'rgba(255, 255, 0, 0.8)'; // Yellow for medium confidence
    } else {
        barColor = 'rgba(0, 255, 0, 0.8)'; // Green for high confidence
    }

    ctx.fillStyle = barColor;
    ctx.beginPath();
    ctx.roundRect(startX, y, confidenceWidth, height, 5);
    ctx.fill();

    // Draw text
    ctx.font = 'bold 14px Arial';
    ctx.fillStyle = 'white';
    ctx.textAlign = 'center';
    ctx.fillText(`Confidence: ${Math.round(confidence * 100)}%`, centerX, y + 20);

    // Draw emotion label
    ctx.font = 'bold 16px Arial';
    ctx.fillStyle = 'white';
    ctx.textAlign = 'center';
    ctx.fillText(`Detected: ${emotion.toUpperCase()}`, centerX, y - 10);
}

// Show error message
function showError(message) {
    if (emotionResult) {
        emotionResult.innerHTML = `
            <div class="alert alert-danger">
                <p><i class="fas fa-exclamation-triangle"></i> ${message}</p>
            </div>
        `;
    }
}

// Polyfill for roundRect for older browsers
if (!CanvasRenderingContext2D.prototype.roundRect) {
    CanvasRenderingContext2D.prototype.roundRect = function(x, y, width, height, radius) {
        if (typeof radius === 'number') {
            radius = {tl: radius, tr: radius, br: radius, bl: radius};
        } else {
            radius = {
                tl: radius || 0,
                tr: radius || 0,
                br: radius || 0,
                bl: radius || 0
            };
        }

        this.beginPath();
        this.moveTo(x + radius.tl, y);
        this.lineTo(x + width - radius.tr, y);
        this.quadraticCurveTo(x + width, y, x + width, y + radius.tr);
        this.lineTo(x + width, y + height - radius.br);
        this.quadraticCurveTo(x + width, y + height, x + width - radius.br, y + height);
        this.lineTo(x + radius.bl, y + height);
        this.quadraticCurveTo(x, y + height, x, y + height - radius.bl);
        this.lineTo(x, y + radius.tl);
        this.quadraticCurveTo(x, y, x + radius.tl, y);
        this.closePath();
        return this;
    };
}

// Polyfill for AbortSignal.timeout for older browsers
if (!AbortSignal.timeout) {
    AbortSignal.timeout = function timeout(ms) {
        const controller = new AbortController();
        setTimeout(() => controller.abort(new DOMException('TimeoutError', 'TimeoutError')), ms);
        return controller.signal;
    };
}

// Initialize when document is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize when face input option is selected
    const faceInputOption = document.getElementById('face-input-option');
    if (faceInputOption) {
        faceInputOption.addEventListener('click', function() {
            // Load face-api.js script if not already loaded
            if (!window.faceapi) {
                loadFaceApiScript().then(() => {
                    initFacialDetection();
                }).catch(error => {
                    console.error('Error loading face-api.js:', error);
                    showError('Error loading facial detection library. Please try another input method.');
                });
            } else {
                initFacialDetection();
            }
        });
    }
});

// Load face-api.js script dynamically
function loadFaceApiScript() {
    return new Promise((resolve, reject) => {
        const script = document.createElement('script');
        script.src = 'assets/js/face-api.min.js';
        script.onload = resolve;
        script.onerror = reject;
        document.head.appendChild(script);
    });
}

// Enhanced real-time emotion display function
function updateRealTimeEmotionDisplay(dominantEmotion, confidence, allEmotions) {
    if (!emotionResult || isProcessing) return;

    // Create emotion bars HTML
    const emotionBars = Object.entries(allEmotions)
        .sort(([,a], [,b]) => b - a) // Sort by confidence descending
        .map(([emotion, prob]) => {
            const percentage = Math.round(prob * 100);
            const barColor = emotion === dominantEmotion ? 'bg-primary' : 'bg-secondary';
            return `
                <div class="mb-1">
                    <div class="d-flex justify-content-between">
                        <small>${emotion.charAt(0).toUpperCase() + emotion.slice(1)}</small>
                        <small>${percentage}%</small>
                    </div>
                    <div class="progress" style="height: 8px;">
                        <div class="progress-bar ${barColor}" style="width: ${percentage}%"></div>
                    </div>
                </div>
            `;
        }).join('');

    // Confidence color based on level
    let confidenceClass = 'text-danger';
    if (confidence > 0.7) confidenceClass = 'text-success';
    else if (confidence > 0.4) confidenceClass = 'text-warning';

    emotionResult.innerHTML = `
        <div class="alert alert-info">
            <div class="row">
                <div class="col-md-6">
                    <h6><i class="fas fa-eye"></i> Live Detection</h6>
                    <p class="mb-1"><strong>Emotion:</strong> ${dominantEmotion.charAt(0).toUpperCase() + dominantEmotion.slice(1)}</p>
                    <p class="mb-2"><strong>Confidence:</strong> <span class="${confidenceClass}">${Math.round(confidence * 100)}%</span></p>
                    <small class="text-muted">Click "Capture My Expression" to analyze</small>
                </div>
                <div class="col-md-6">
                    <h6><i class="fas fa-chart-bar"></i> All Emotions</h6>
                    ${emotionBars}
                </div>
            </div>
        </div>
    `;
}

// Enhanced emotion history tracking
let emotionHistory = [];

// Get smoothed emotion from history
function getSmoothedEmotion() {
    if (emotionHistory.length === 0) return null;

    // Count occurrences of each emotion in recent history
    const emotionCounts = {};
    const recentHistory = emotionHistory.slice(-5); // Last 5 detections

    recentHistory.forEach(entry => {
        emotionCounts[entry.emotion] = (emotionCounts[entry.emotion] || 0) + entry.confidence;
    });

    // Find emotion with highest weighted count
    let bestEmotion = null;
    let bestScore = 0;

    for (const [emotion, score] of Object.entries(emotionCounts)) {
        if (score > bestScore) {
            bestEmotion = emotion;
            bestScore = score;
        }
    }

    return {
        emotion: bestEmotion,
        confidence: bestScore / recentHistory.length
    };
}

// Make functions globally available
window.captureAndAnalyze = captureAndAnalyze;
window.initFacialDetection = initFacialDetection;
window.updateRealTimeEmotionDisplay = updateRealTimeEmotionDisplay;
window.getSmoothedEmotion = getSmoothedEmotion;
