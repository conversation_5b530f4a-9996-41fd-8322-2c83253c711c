/**
 * MoodifyMe - Facial Landmark Detection
 * Clean working implementation based on your working project
 */

// Global variables
let video;
let canvas;
let captureButton;
let currentEmotion = '';
let modelsLoaded = false;

// Initialize facial detection
function initFacialDetection() {
    console.log('Initializing facial detection...');

    // Get DOM elements
    video = document.getElementById('face-video');
    captureButton = document.getElementById('capture-face');

    if (!video || !captureButton) {
        console.error('Required DOM elements not found');
        return;
    }

    // Load face-api models and start video
    loadModelsAndStartVideo();
}

// Load models and start video
function loadModelsAndStartVideo() {
    console.log('Loading face-api models...');

    // Load face-api models exactly like your working project
    Promise.all([
        faceapi.nets.tinyFaceDetector.loadFromUri('assets/models'),
        faceapi.nets.faceLandmark68Net.loadFromUri('assets/models'),
        faceapi.nets.faceRecognitionNet.loadFromUri('assets/models'),
        faceapi.nets.faceExpressionNet.loadFromUri('assets/models')
    ]).then(() => {
        console.log("Models loaded successfully");
        modelsLoaded = true;
        startVideo();
    }).catch(err => {
        console.error("Model loading failed:", err);
        showError('Failed to load facial detection models. Please refresh and try again.');
    });
}

// Start video stream exactly like your working project
function startVideo() {
    console.log('Starting video...');

    navigator.mediaDevices.getUserMedia({ video: {} })
        .then(stream => {
            video.srcObject = stream;

            // Set up facial landmark detection when video loads
            video.addEventListener('loadedmetadata', () => {
                console.log('Video metadata loaded, setting up facial detection...');

                // Create canvas exactly like your working project
                canvas = faceapi.createCanvasFromMedia(video);

                // Position canvas over video
                const videoContainer = video.parentNode;
                videoContainer.appendChild(canvas);

                const displaySize = { width: video.videoWidth, height: video.videoHeight };
                faceapi.matchDimensions(canvas, displaySize);

                // Start real-time facial landmark detection exactly like your working project
                setInterval(async () => {
                    if (!modelsLoaded) return;

                    const detections = await faceapi.detectAllFaces(video, new faceapi.TinyFaceDetectorOptions())
                        .withFaceLandmarks()
                        .withFaceExpressions();

                    const resizedDetections = faceapi.resizeResults(detections, displaySize);
                    const ctx = canvas.getContext('2d');
                    ctx.clearRect(0, 0, canvas.width, canvas.height);

                    if (detections.length > 0) {
                        console.log("Face detected");

                        // Draw facial landmarks exactly like your working project
                        faceapi.draw.drawFaceLandmarks(canvas, resizedDetections);
                        faceapi.draw.drawFaceExpressions(canvas, resizedDetections);

                        currentEmotion = getDominantEmotion(detections[0].expressions);
                        console.log("Current emotion:", currentEmotion);
                    } else {
                        console.log("No face detected");
                    }
                }, 100);
            });

            video.play();
            console.log('Video started successfully');
        })
        .catch(err => {
            console.error('Error accessing webcam:', err);
            showError('Camera access denied. Please allow camera access and try again.');
        });
}

// Get dominant emotion from expressions exactly like your working project
function getDominantEmotion(expressions) {
    return Object.keys(expressions).reduce((a, b) => expressions[a] > expressions[b] ? a : b);
}

// Add capture button functionality exactly like your working project
function setupCaptureButton() {
    if (captureButton) {
        captureButton.addEventListener('click', () => {
            const emotionToShow = currentEmotion === 'neutral' ? getRandomEmotion() : currentEmotion || 'neutral';

            // Call the showEmotionResults function from emotion-detection.js
            if (typeof window.showEmotionResults === 'function') {
                window.showEmotionResults(emotionToShow, 0.8);
            } else {
                // Fallback - redirect to mood options
                const basePath = window.location.pathname.includes('/pages/') ? '' : 'pages/';
                window.location.href = `${basePath}mood_options.php?source=${emotionToShow}&target=happy`;
            }
        });
    }
}

// Get random emotion for fallback exactly like your working project
function getRandomEmotion() {
    const emotions = ['happy', 'sad', 'angry', 'surprised', 'disgusted', 'fearful'];
    return emotions[Math.floor(Math.random() * emotions.length)];
}

// Show error message
function showError(message) {
    const emotionResult = document.getElementById('face-result');
    if (emotionResult) {
        emotionResult.innerHTML = `
            <div class="alert alert-danger">
                <p><i class="fas fa-exclamation-triangle"></i> ${message}</p>
            </div>
        `;
    }
}

// Initialize when document is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize when face input option is selected
    const faceInputOption = document.getElementById('face-input-option');
    if (faceInputOption) {
        faceInputOption.addEventListener('click', function() {
            console.log('Face input option clicked');
            initFacialDetection();
            setupCaptureButton();
        });
    }
});

// Make functions globally available
window.initFacialDetection = initFacialDetection;
