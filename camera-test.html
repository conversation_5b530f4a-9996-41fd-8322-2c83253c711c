<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Camera Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #1a1a1a;
            color: white;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }
        
        video {
            width: 640px;
            height: 480px;
            border: 2px solid #E55100;
            border-radius: 10px;
            background: #333;
        }
        
        button {
            background: #E55100;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
        }
        
        button:hover {
            background: #D32F2F;
        }
        
        .log {
            background: #333;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            text-align: left;
            font-family: monospace;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎥 Camera Test</h1>
        <p>Testing basic camera functionality</p>
        
        <video id="testVideo" autoplay muted></video>
        
        <div>
            <button onclick="startCamera()">Start Camera</button>
            <button onclick="stopCamera()">Stop Camera</button>
            <button onclick="checkPermissions()">Check Permissions</button>
        </div>
        
        <div id="log" class="log"></div>
    </div>

    <script>
        let video = document.getElementById('testVideo');
        let currentStream = null;
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        async function checkPermissions() {
            log('🔍 Checking camera permissions...');
            
            try {
                const result = await navigator.permissions.query({ name: 'camera' });
                log(`📋 Camera permission: ${result.state}`);
                
                result.addEventListener('change', () => {
                    log(`📋 Permission changed to: ${result.state}`);
                });
                
            } catch (error) {
                log(`❌ Error checking permissions: ${error.message}`);
            }
            
            // Check if getUserMedia is available
            if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
                log('✅ getUserMedia is available');
            } else {
                log('❌ getUserMedia is not available');
            }
            
            // Check protocol
            log(`🌐 Protocol: ${window.location.protocol}`);
            log(`🏠 Hostname: ${window.location.hostname}`);
        }
        
        async function startCamera() {
            log('📹 Starting camera...');
            
            try {
                // Stop any existing stream first
                if (currentStream) {
                    stopCamera();
                }
                
                log('📱 Requesting camera access...');
                
                const constraints = {
                    video: {
                        width: { ideal: 640 },
                        height: { ideal: 480 },
                        facingMode: 'user'
                    }
                };
                
                currentStream = await navigator.mediaDevices.getUserMedia(constraints);
                log('✅ Camera access granted!');
                
                video.srcObject = currentStream;
                log('📺 Video stream assigned to element');
                
                video.onloadedmetadata = () => {
                    log('📊 Video metadata loaded');
                    log(`📐 Video dimensions: ${video.videoWidth}x${video.videoHeight}`);
                };
                
                video.onplay = () => {
                    log('▶️ Video started playing');
                };
                
                video.onerror = (error) => {
                    log(`❌ Video error: ${error}`);
                };
                
            } catch (error) {
                log(`❌ Camera error: ${error.name} - ${error.message}`);
                
                if (error.name === 'NotAllowedError') {
                    log('🚫 Camera access denied by user');
                } else if (error.name === 'NotFoundError') {
                    log('📷 No camera found');
                } else if (error.name === 'NotReadableError') {
                    log('🔒 Camera is already in use');
                }
            }
        }
        
        function stopCamera() {
            log('⏹️ Stopping camera...');
            
            if (currentStream) {
                const tracks = currentStream.getTracks();
                tracks.forEach(track => {
                    track.stop();
                    log(`🛑 Stopped track: ${track.kind}`);
                });
                currentStream = null;
                video.srcObject = null;
                log('✅ Camera stopped');
            } else {
                log('ℹ️ No active camera stream');
            }
        }
        
        // Auto-check on load
        window.onload = () => {
            log('🚀 Camera test loaded');
            checkPermissions();
        };
    </script>
</body>
</html>
