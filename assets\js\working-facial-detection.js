/**
 * MoodifyMe - Working Facial Detection System
 * Based on the working Movie-Recommendations project
 */

// Global variables - exactly like working project
let video;
let captureBtn;
let currentEmotion = '';
let modelsLoaded = false;

// Initialize facial detection - exactly like working project
function initFacialDetection() {
    console.log('🚀 Initializing facial detection...');

    // Get DOM elements
    video = document.getElementById('face-video');
    captureBtn = document.getElementById('capture-face');

    if (!video) {
        console.error('❌ Video element not found');
        return;
    }

    // Load face-api models exactly like working project
    Promise.all([
        faceapi.nets.tinyFaceDetector.loadFromUri('assets/models'),
        faceapi.nets.faceLandmark68Net.loadFromUri('assets/models'),
        faceapi.nets.faceRecognitionNet.loadFromUri('assets/models'),
        faceapi.nets.faceExpressionNet.loadFromUri('assets/models')
    ]).then(() => {
        console.log("✅ Models loaded successfully");
        modelsLoaded = true;
        startVideo();
        setupCaptureButton();
    }).catch(err => {
        console.error("❌ Model loading failed:", err);
        showError('Failed to load facial detection models. Please check your internet connection.');
    });
}

// Start video exactly like working project
function startVideo() {
    console.log('📹 Starting video...');

    navigator.mediaDevices.getUserMedia({ video: {} })
        .then(stream => {
            video.srcObject = stream;
            video.play();
            console.log('✅ Video started successfully');

            // Show success message
            const emotionResult = document.getElementById('face-result');
            if (emotionResult) {
                emotionResult.innerHTML = `
                    <div class="alert alert-success">
                        <p><i class="fas fa-check-circle"></i> Camera started successfully!</p>
                        <p>Position your face in the camera view to see facial landmarks.</p>
                    </div>
                `;
            }

            // Initialize video detection after video starts
            initVideoDetection();
        })
        .catch(err => {
            console.error('❌ Error accessing webcam:', err);
            showError('Error accessing camera. Please allow camera access and try again.');
        });
}

// Video loaded event handler function - exactly like working project
function setupVideoDetection() {
    if (!video) return;

    video.addEventListener('loadedmetadata', () => {
        console.log('📊 Video metadata loaded, starting detection...');

        const canvas = faceapi.createCanvasFromMedia(video);
        const videoContainer = video.parentElement;
        videoContainer.style.position = 'relative';
        videoContainer.appendChild(canvas);

        const displaySize = { width: video.videoWidth, height: video.videoHeight };
        faceapi.matchDimensions(canvas, displaySize);

        canvas.width = displaySize.width;
        canvas.height = displaySize.height;

        // Position canvas over video - exactly like working project
        canvas.style.position = 'absolute';
        canvas.style.top = '0';
        canvas.style.left = '0';
        canvas.style.pointerEvents = 'none';
        canvas.style.zIndex = '10';

        // Start detection loop - exactly like working project
        setInterval(async () => {
            if (!modelsLoaded) return;

            const detections = await faceapi.detectAllFaces(video, new faceapi.TinyFaceDetectorOptions())
                .withFaceLandmarks()
                .withFaceExpressions();

            const resizedDetections = faceapi.resizeResults(detections, displaySize);
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // Translate the canvas down by 2vh - exactly like working project
            ctx.save();
            ctx.translate(0, 0); // Shift down by 2vh

            if (detections.length > 0) {
                console.log("✅ Face detected");

                // Draw facial landmarks - this creates the magenta lines!
                faceapi.draw.drawFaceLandmarks(canvas, resizedDetections);
                faceapi.draw.drawFaceExpressions(canvas, resizedDetections);

                currentEmotion = getDominantEmotion(detections[0].expressions);
                console.log("Current emotion:", currentEmotion);

                // Update emotion display
                updateEmotionDisplay(currentEmotion, detections[0].expressions);
            } else {
                console.log("No face detected");
                updateEmotionDisplay(null);
            }
            ctx.restore(); // Restore the context to its original state
        }, 100);
    });
}

// Call setup function after video is initialized
function initVideoDetection() {
    // Get DOM elements after they're available
    video = document.getElementById('face-video');
    captureBtn = document.getElementById('capture-face');

    if (video) {
        setupVideoDetection();
    }
}

// Setup capture button event handler
function setupCaptureButton() {
    const captureBtn = document.getElementById('capture-face');
    if (captureBtn) {
        captureBtn.addEventListener('click', () => {
            console.log('🎯 Capture button clicked');

            const emotionToShow = currentEmotion === 'neutral' ? getRandomEmotion() : currentEmotion || 'neutral';
            console.log('Using emotion:', emotionToShow);

            // Call the showEmotionResults function from emotion-detection.js
            if (typeof window.showEmotionResults === 'function') {
                window.showEmotionResults(emotionToShow, 0.8);
            } else {
                // Fallback - redirect to mood options
                const basePath = window.location.pathname.includes('/pages/') ? '' : 'pages/';
                window.location.href = `${basePath}mood_options.php?source=${emotionToShow}&target=happy`;
            }
        });
    }
}

// Helper functions - exactly like working project
function getDominantEmotion(expressions) {
    return Object.keys(expressions).reduce((a, b) => expressions[a] > expressions[b] ? a : b);
}

function getRandomEmotion() {
    const emotions = ['happy', 'sad', 'angry', 'surprised', 'disgusted', 'fearful'];
    return emotions[Math.floor(Math.random() * emotions.length)];
}

function updateEmotionDisplay(emotion, expressions) {
    const emotionDisplay = document.getElementById('emotion-display');
    if (emotionDisplay) {
        if (emotion) {
            const confidence = expressions ? Math.max(...Object.values(expressions)) : 0.8;
            emotionDisplay.innerHTML = `
                <div class="emotion-info">
                    <strong>Detected: ${emotion}</strong><br>
                    <small>Confidence: ${Math.round(confidence * 100)}%</small>
                </div>
            `;
        } else {
            emotionDisplay.innerHTML = '<small class="text-muted">No face detected</small>';
        }
    }
}

function showError(message) {
    const emotionResult = document.getElementById('face-result');
    if (emotionResult) {
        emotionResult.innerHTML = `
            <div class="alert alert-danger">
                <p><i class="fas fa-exclamation-triangle"></i> ${message}</p>
            </div>
        `;
    }
}

// Make functions globally available
window.initFacialDetection = initFacialDetection;
window.currentEmotion = currentEmotion;

console.log('✅ Working facial detection script loaded!');
