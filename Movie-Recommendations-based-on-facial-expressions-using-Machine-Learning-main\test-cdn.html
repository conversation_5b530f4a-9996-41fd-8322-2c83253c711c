<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MoodifyMe - CDN Test</title>
    <!-- Use CDN version of face-api.js -->
    <script defer src="https://cdn.jsdelivr.net/npm/face-api.js@0.22.2/dist/face-api.min.js"></script>
    <style>
        body {
            background-color: #1a1a1a;
            color: white;
            font-family: Arial, sans-serif;
            padding: 20px;
            text-align: center;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        h1 {
            color: #E55100;
            margin-bottom: 30px;
        }
        
        .video-container {
            position: relative;
            display: inline-block;
            margin: 20px 0;
        }
        
        video {
            width: 640px;
            height: 480px;
            border-radius: 10px;
            border: 2px solid #E55100;
        }
        
        canvas {
            position: absolute;
            top: 0;
            left: 0;
            border-radius: 10px;
        }
        
        button {
            background: #E55100;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
            transition: background 0.3s;
        }
        
        button:hover {
            background: #D32F2F;
        }
        
        button:disabled {
            background: #666;
            cursor: not-allowed;
        }
        
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 6px;
            background: #333;
        }
        
        .emotion-display {
            font-size: 24px;
            font-weight: bold;
            color: #FFD54F;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎭 MoodifyMe - CDN Face-API Test</h1>
        <p>Testing with CDN version of face-api.js</p>
        
        <div class="video-container">
            <video id="video" autoplay muted></video>
        </div>
        
        <div class="controls">
            <button id="startBtn">Start Detection</button>
            <button id="captureBtn" disabled>Capture Emotion</button>
        </div>
        
        <div class="status" id="status">
            Click "Start Detection" to begin
        </div>
        
        <div class="emotion-display" id="emotionDisplay"></div>
    </div>

    <script>
        const video = document.getElementById('video');
        const startBtn = document.getElementById('startBtn');
        const captureBtn = document.getElementById('captureBtn');
        const status = document.getElementById('status');
        const emotionDisplay = document.getElementById('emotionDisplay');
        
        let currentEmotion = '';
        let isDetecting = false;

        // Load models from CDN
        async function loadModels() {
            try {
                status.innerHTML = '🔄 Loading models from CDN...';
                
                // Load models from CDN
                await Promise.all([
                    faceapi.nets.tinyFaceDetector.loadFromUri('https://cdn.jsdelivr.net/npm/face-api.js@0.22.2/weights'),
                    faceapi.nets.faceLandmark68Net.loadFromUri('https://cdn.jsdelivr.net/npm/face-api.js@0.22.2/weights'),
                    faceapi.nets.faceRecognitionNet.loadFromUri('https://cdn.jsdelivr.net/npm/face-api.js@0.22.2/weights'),
                    faceapi.nets.faceExpressionNet.loadFromUri('https://cdn.jsdelivr.net/npm/face-api.js@0.22.2/weights')
                ]);
                
                status.innerHTML = '✅ CDN Models loaded successfully!';
                console.log("CDN Models loaded successfully");
                return true;
            } catch (error) {
                status.innerHTML = '❌ Error loading CDN models: ' + error.message;
                console.error("CDN Model loading failed:", error);
                return false;
            }
        }

        // Start video
        async function startVideo() {
            try {
                status.innerHTML = '📹 Starting camera...';
                
                const stream = await navigator.mediaDevices.getUserMedia({ video: {} });
                video.srcObject = stream;
                
                status.innerHTML = '✅ Camera started! Initializing detection...';
                startBtn.disabled = true;
                captureBtn.disabled = false;
                
                return true;
            } catch (error) {
                status.innerHTML = '❌ Error accessing camera: ' + error.message;
                console.error('Error accessing webcam:', error);
                return false;
            }
        }

        // Start detection when video is ready
        video.addEventListener('loadedmetadata', () => {
            const canvas = faceapi.createCanvasFromMedia(video);
            document.querySelector('.video-container').appendChild(canvas);

            const displaySize = { width: video.videoWidth, height: video.videoHeight };
            faceapi.matchDimensions(canvas, displaySize);

            canvas.width = displaySize.width;
            canvas.height = displaySize.height;

            // Start detection loop - exactly like working project
            setInterval(async () => {
                if (!isDetecting) return;
                
                const detections = await faceapi.detectAllFaces(video, new faceapi.TinyFaceDetectorOptions())
                    .withFaceLandmarks()
                    .withFaceExpressions();

                const resizedDetections = faceapi.resizeResults(detections, displaySize);
                const ctx = canvas.getContext('2d');
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                if (detections.length > 0) {
                    console.log("Face detected with CDN");
                    
                    // Draw landmarks exactly like working project
                    faceapi.draw.drawFaceLandmarks(canvas, resizedDetections);
                    faceapi.draw.drawFaceExpressions(canvas, resizedDetections);

                    currentEmotion = getDominantEmotion(detections[0].expressions);
                    const confidence = Math.max(...Object.values(detections[0].expressions));
                    
                    emotionDisplay.innerHTML = `Emotion: ${currentEmotion} (${Math.round(confidence * 100)}%)`;
                    
                    console.log("CDN Current emotion:", currentEmotion);
                } else {
                    console.log("No face detected");
                    emotionDisplay.innerHTML = 'No face detected';
                }
            }, 100);
        });

        // Get dominant emotion
        function getDominantEmotion(expressions) {
            return Object.keys(expressions).reduce((a, b) => expressions[a] > expressions[b] ? a : b);
        }

        // Event listeners
        startBtn.addEventListener('click', async () => {
            const modelsLoaded = await loadModels();
            if (modelsLoaded) {
                const videoStarted = await startVideo();
                if (videoStarted) {
                    isDetecting = true;
                    status.innerHTML = '🎯 CDN Facial detection active!';
                }
            }
        });

        captureBtn.addEventListener('click', () => {
            if (currentEmotion) {
                alert(`CDN Captured Emotion: ${currentEmotion}\n\nThis proves the CDN version works!`);
            } else {
                alert('No emotion detected yet.');
            }
        });
    </script>
</body>
</html>
