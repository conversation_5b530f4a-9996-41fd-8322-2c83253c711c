<?php
/**
 * User Class
 * 
 * Handles user-related operations like registration, login, and profile management.
 */
class User {
    private $db;
    
    /**
     * Constructor - Initialize database connection
     */
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * Register a new user
     * 
     * @param array $data User data (username, email, password)
     * @return boolean True if registration successful, false otherwise
     */
    public function register($data) {
        // Prepare query
        $this->db->query('INSERT INTO users (username, email, password) VALUES (:username, :email, :password)');
        
        // Hash password
        $hashedPassword = password_hash($data['password'], PASSWORD_DEFAULT);
        
        // Bind values
        $this->db->bind(':username', $data['username']);
        $this->db->bind(':email', $data['email']);
        $this->db->bind(':password', $hashedPassword);
        
        // Execute
        if($this->db->execute()) {
            return true;
        } else {
            return false;
        }
    }
    
    /**
     * Login user
     * 
     * @param string $email User email
     * @param string $password User password
     * @return mixed User data if login successful, false otherwise
     */
    public function login($email, $password) {
        // Prepare query
        $this->db->query('SELECT * FROM users WHERE email = :email');
        
        // Bind value
        $this->db->bind(':email', $email);
        
        // Get single record
        $row = $this->db->single();
        
        if($row) {
            $hashedPassword = $row['password'];
            
            // Verify password
            if(password_verify($password, $hashedPassword)) {
                return $row;
            }
        }
        
        return false;
    }
    
    /**
     * Find user by email
     * 
     * @param string $email User email
     * @return boolean True if user exists, false otherwise
     */
    public function findUserByEmail($email) {
        // Prepare query
        $this->db->query('SELECT * FROM users WHERE email = :email');
        
        // Bind value
        $this->db->bind(':email', $email);
        
        // Get single record
        $row = $this->db->single();
        
        // Check if email exists
        if($this->db->rowCount() > 0) {
            return true;
        } else {
            return false;
        }
    }
    
    /**
     * Find user by username
     * 
     * @param string $username Username
     * @return boolean True if user exists, false otherwise
     */
    public function findUserByUsername($username) {
        // Prepare query
        $this->db->query('SELECT * FROM users WHERE username = :username');
        
        // Bind value
        $this->db->bind(':username', $username);
        
        // Get single record
        $row = $this->db->single();
        
        // Check if username exists
        if($this->db->rowCount() > 0) {
            return true;
        } else {
            return false;
        }
    }
    
    /**
     * Get user by ID
     * 
     * @param int $id User ID
     * @return mixed User data if found, false otherwise
     */
    public function getUserById($id) {
        // Prepare query
        $this->db->query('SELECT * FROM users WHERE id = :id');
        
        // Bind value
        $this->db->bind(':id', $id);
        
        // Get single record
        $row = $this->db->single();
        
        return $row;
    }
    
    /**
     * Get user's emotion history
     * 
     * @param int $userId User ID
     * @param int $limit Number of records to return
     * @return array Emotion history
     */
    public function getUserEmotionHistory($userId, $limit = 10) {
        // Prepare query
        $this->db->query('SELECT ue.*, e.name as emotion_name 
                         FROM user_emotions ue 
                         JOIN emotions e ON ue.emotion_id = e.id 
                         WHERE ue.user_id = :user_id 
                         ORDER BY ue.timestamp DESC 
                         LIMIT :limit');
        
        // Bind values
        $this->db->bind(':user_id', $userId);
        $this->db->bind(':limit', $limit, PDO::PARAM_INT);
        
        // Get results
        return $this->db->resultSet();
    }
    
    /**
     * Get user's recommendation history
     * 
     * @param int $userId User ID
     * @param int $limit Number of records to return
     * @return array Recommendation history
     */
    public function getUserRecommendationHistory($userId, $limit = 10) {
        // Prepare query
        $this->db->query('SELECT ur.*, r.title, r.type, 
                         e1.name as source_emotion, e2.name as target_emotion 
                         FROM user_recommendations ur 
                         JOIN recommendations r ON ur.recommendation_id = r.id 
                         JOIN emotions e1 ON ur.source_emotion_id = e1.id 
                         JOIN emotions e2 ON ur.target_emotion_id = e2.id 
                         WHERE ur.user_id = :user_id 
                         ORDER BY ur.timestamp DESC 
                         LIMIT :limit');
        
        // Bind values
        $this->db->bind(':user_id', $userId);
        $this->db->bind(':limit', $limit, PDO::PARAM_INT);
        
        // Get results
        return $this->db->resultSet();
    }
}
?>
