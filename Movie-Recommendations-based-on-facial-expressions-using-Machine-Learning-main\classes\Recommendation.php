<?php
/**
 * Recommendation Class
 * 
 * Handles recommendation generation and processing.
 */
class Recommendation {
    private $db;
    
    /**
     * Constructor - Initialize database connection
     */
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * Get recommendations based on emotion
     * 
     * @param string $emotionName Emotion name
     * @param string $type Recommendation type (movie, music, meal, joke)
     * @param int $limit Number of recommendations to return
     * @return array List of recommendations
     */
    public function getRecommendationsByEmotion($emotionName, $type = 'movie', $limit = 5) {
        // Get emotion data
        $emotion = $this->getEmotionByName($emotionName);
        
        if (!$emotion) {
            return [];
        }
        
        // Get recommendation categories for this emotion
        $categories = explode(',', $emotion['recommendation_categories']);
        
        // Prepare query with category filtering
        $placeholders = implode(',', array_fill(0, count($categories), '?'));
        
        $sql = "SELECT * FROM recommendations 
                WHERE type = ? 
                AND (emotion_tags LIKE ? ";
        
        // Add each category as a condition
        foreach ($categories as $category) {
            $sql .= " OR emotion_tags LIKE ? ";
        }
        
        $sql .= ") ORDER BY RAND() LIMIT ?";
        
        $this->db->query($sql);
        
        // Bind type
        $this->db->bind(1, $type);
        
        // Bind emotion name
        $this->db->bind(2, '%' . $emotionName . '%');
        
        // Bind categories
        $paramIndex = 3;
        foreach ($categories as $category) {
            $this->db->bind($paramIndex, '%' . trim($category) . '%');
            $paramIndex++;
        }
        
        // Bind limit
        $this->db->bind($paramIndex, $limit, PDO::PARAM_INT);
        
        // Get results
        return $this->db->resultSet();
    }
    
    /**
     * Get emotion by name (helper method)
     * 
     * @param string $name Emotion name
     * @return mixed Emotion data if found, false otherwise
     */
    private function getEmotionByName($name) {
        // Prepare query
        $this->db->query('SELECT * FROM emotions WHERE name = :name');
        
        // Bind value
        $this->db->bind(':name', $name);
        
        // Get single record
        return $this->db->single();
    }
    
    /**
     * Record user recommendation
     * 
     * @param int $userId User ID
     * @param int $recommendationId Recommendation ID
     * @param int $sourceEmotionId Source emotion ID
     * @param int $targetEmotionId Target emotion ID
     * @return boolean True if successful, false otherwise
     */
    public function recordUserRecommendation($userId, $recommendationId, $sourceEmotionId, $targetEmotionId) {
        // Prepare query
        $this->db->query('INSERT INTO user_recommendations 
                         (user_id, recommendation_id, source_emotion_id, target_emotion_id) 
                         VALUES (:user_id, :recommendation_id, :source_emotion_id, :target_emotion_id)');
        
        // Bind values
        $this->db->bind(':user_id', $userId);
        $this->db->bind(':recommendation_id', $recommendationId);
        $this->db->bind(':source_emotion_id', $sourceEmotionId);
        $this->db->bind(':target_emotion_id', $targetEmotionId);
        
        // Execute
        return $this->db->execute();
    }
    
    /**
     * Rate a recommendation
     * 
     * @param int $userRecommendationId User recommendation ID
     * @param int $rating Rating (1-5)
     * @return boolean True if successful, false otherwise
     */
    public function rateRecommendation($userRecommendationId, $rating) {
        // Validate rating
        if ($rating < 1 || $rating > 5) {
            return false;
        }
        
        // Prepare query
        $this->db->query('UPDATE user_recommendations 
                         SET rating = :rating 
                         WHERE id = :id');
        
        // Bind values
        $this->db->bind(':rating', $rating);
        $this->db->bind(':id', $userRecommendationId);
        
        // Execute
        return $this->db->execute();
    }
    
    /**
     * Get movie recommendations from TMDB API
     * 
     * @param string $emotionName Emotion name
     * @param int $limit Number of recommendations to return
     * @return array List of movie recommendations
     */
    public function getMovieRecommendationsFromAPI($emotionName, $limit = 5) {
        // Get emotion data
        $emotion = $this->getEmotionByName($emotionName);
        
        if (!$emotion) {
            return [];
        }
        
        // Get recommendation categories for this emotion
        $categories = explode(',', $emotion['recommendation_categories']);
        $category = trim($categories[0]); // Use the first category
        
        // Call TMDB API
        $apiKey = TMDB_API_KEY;
        $url = "https://api.themoviedb.org/3/discover/movie?api_key={$apiKey}&with_genres={$category}&sort_by=popularity.desc&page=1";
        
        $response = file_get_contents($url);
        $data = json_decode($response, true);
        
        $movies = [];
        
        if (isset($data['results'])) {
            $count = 0;
            foreach ($data['results'] as $movie) {
                if ($count >= $limit) break;
                
                $movies[] = [
                    'id' => $movie['id'],
                    'title' => $movie['title'],
                    'description' => $movie['overview'],
                    'image_url' => "https://image.tmdb.org/t/p/w500" . $movie['poster_path'],
                    'type' => 'movie'
                ];
                
                $count++;
            }
        }
        
        return $movies;
    }
}
?>
