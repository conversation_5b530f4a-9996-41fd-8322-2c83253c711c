<?php
/**
 * Recommendations API
 * 
 * Handles recommendation generation and processing requests.
 */

// Initialize session and include required files
session_start();
require_once '../config/config.php';
require_once '../config/database.php';
require_once '../classes/Database.php';
require_once '../classes/Emotion.php';
require_once '../classes/Recommendation.php';

// Set content type to JSON
header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode([
        'success' => false,
        'message' => 'User not authenticated'
    ]);
    exit;
}

$userId = $_SESSION['user_id'];
$emotionObj = new Emotion();
$recommendationObj = new Recommendation();

// Get the action from the request
$action = isset($_GET['action']) ? $_GET['action'] : '';

switch ($action) {
    case 'get':
        // Get recommendations
        getRecommendations();
        break;
        
    case 'rate':
        // Rate a recommendation
        rateRecommendation();
        break;
        
    case 'record':
        // Record user recommendation
        recordRecommendation();
        break;
        
    case 'get-movies':
        // Get movie recommendations from API
        getMovieRecommendations();
        break;
        
    default:
        // Invalid action
        echo json_encode([
            'success' => false,
            'message' => 'Invalid action'
        ]);
        break;
}

/**
 * Get recommendations based on emotion
 */
function getRecommendations() {
    global $recommendationObj;
    
    // Get parameters
    $emotionName = isset($_GET['emotion']) ? $_GET['emotion'] : 'neutral';
    $type = isset($_GET['type']) ? $_GET['type'] : 'movie';
    $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 10;
    
    // Get recommendations
    $recommendations = $recommendationObj->getRecommendationsByEmotion($emotionName, $type, $limit);
    
    echo json_encode([
        'success' => true,
        'recommendations' => $recommendations
    ]);
}

/**
 * Rate a recommendation
 */
function rateRecommendation() {
    global $recommendationObj;
    
    // Get data from POST request
    $data = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($data['id']) || !isset($data['rating'])) {
        echo json_encode([
            'success' => false,
            'message' => 'Missing required parameters'
        ]);
        return;
    }
    
    // Rate recommendation
    $result = $recommendationObj->rateRecommendation($data['id'], $data['rating']);
    
    if ($result) {
        echo json_encode([
            'success' => true,
            'message' => 'Recommendation rated successfully'
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Failed to rate recommendation'
        ]);
    }
}

/**
 * Record user recommendation
 */
function recordRecommendation() {
    global $recommendationObj, $emotionObj, $userId;
    
    // Get data from POST request
    $data = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($data['recommendation_id']) || !isset($data['source_emotion']) || !isset($data['target_emotion'])) {
        echo json_encode([
            'success' => false,
            'message' => 'Missing required parameters'
        ]);
        return;
    }
    
    // Get emotion IDs
    $sourceEmotion = $emotionObj->getEmotionByName($data['source_emotion']);
    $targetEmotion = $emotionObj->getEmotionByName($data['target_emotion']);
    
    if (!$sourceEmotion || !$targetEmotion) {
        echo json_encode([
            'success' => false,
            'message' => 'Invalid emotion'
        ]);
        return;
    }
    
    // Record recommendation
    $result = $recommendationObj->recordUserRecommendation(
        $userId,
        $data['recommendation_id'],
        $sourceEmotion['id'],
        $targetEmotion['id']
    );
    
    if ($result) {
        echo json_encode([
            'success' => true,
            'message' => 'Recommendation recorded successfully'
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Failed to record recommendation'
        ]);
    }
}

/**
 * Get movie recommendations from TMDB API
 */
function getMovieRecommendations() {
    global $recommendationObj;
    
    // Get parameters
    $emotionName = isset($_GET['emotion']) ? $_GET['emotion'] : 'neutral';
    $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 10;
    
    // Get recommendations
    $recommendations = $recommendationObj->getMovieRecommendationsFromAPI($emotionName, $limit);
    
    echo json_encode([
        'success' => true,
        'recommendations' => $recommendations
    ]);
}
?>
