'use strict';

import { imageBaseURL } from "./api.js";

//movie card

export function createMovieCard(movie) {
    const {
        poster_path,
        title,
        vote_average,
        release_date,
        id
    } = movie;

    const card = document.createElement("div");
    card.classList.add("movie-card");

    card.innerHTML = `
    <figure class="poster-box card-banner">
        <img src="${imageBaseURL}w342${poster_path}" alt="${title}" class="img-cover" loading="lazy">
    </figure>
    <h4 class="title">${title}</h4>
    <div class="meta-list">
        <div class="meta-item">
            <img src="icons/star.png" width="22" height="22" loading="lazy" alt="rating">
            <span class="span">${vote_average.toFixed(1)}</span>
        </div>
        <div class="card-badge">${release_date?.split("-")[0] ?? "Not released!"}</div>
    </div>
    <a href="detail.html" class="card-button" title="${title}" onclick="getMovieDetail(${id})"></a>
    `;

    return card;
}