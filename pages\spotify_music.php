<?php
/**
 * MoodifyMe - Spotify Music Recommendations Page
 *
 * This page displays music recommendations from Spotify based on the user's mood
 */

// Include configuration and functions
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/db_connect.php';
require_once __DIR__ . '/../includes/spotify_api.php';

// Start session
session_start();

// For debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    // Redirect to login page
    redirect(APP_URL . '/pages/login.php');
}

// Get user ID
$userId = $_SESSION['user_id'];

// Get source and target emotions from URL parameters
$sourceEmotion = isset($_GET['source']) ? sanitizeInput($_GET['source']) : '';
$targetEmotion = isset($_GET['target']) ? sanitizeInput($_GET['target']) : '';
$emotionId = isset($_GET['emotion_id']) ? sanitizeInput($_GET['emotion_id']) : '';

// If source or target emotion is not provided, redirect to home page
if (empty($sourceEmotion) || empty($targetEmotion)) {
    // Check if user has recent emotions
    $stmt = $conn->prepare("SELECT emotion_type FROM emotions WHERE user_id = ? ORDER BY created_at DESC LIMIT 1");
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 1) {
        $row = $result->fetch_assoc();
        $sourceEmotion = $row['emotion_type'];
        $targetEmotion = 'happy'; // Default target emotion
    } else {
        // Redirect to home page
        redirect(APP_URL);
    }
}

// Get music recommendations from database
$stmt = $conn->prepare("SELECT * FROM recommendations WHERE source_emotion = ? AND target_emotion = ? AND type = 'music' ORDER BY RAND() LIMIT 10");
$stmt->bind_param("ss", $sourceEmotion, $targetEmotion);
$stmt->execute();
$result = $stmt->get_result();
$musicRecommendations = [];

while ($row = $result->fetch_assoc()) {
    // Parse additional data if available
    $additionalData = null;
    if (isset($row['additional_data'])) {
        $additionalData = json_decode($row['additional_data'], true);
    }

    // Add additional data to the recommendation
    $row['preview_url'] = $additionalData && isset($additionalData['preview_url']) ? $additionalData['preview_url'] : null;
    $row['artist'] = $additionalData && isset($additionalData['artist']) ? $additionalData['artist'] : '';
    $row['album'] = $additionalData && isset($additionalData['album']) ? $additionalData['album'] : '';

    $musicRecommendations[] = $row;
}

// If no recommendations found in database, fetch from Spotify API
if (empty($musicRecommendations)) {
    $spotifyRecommendations = getSpotifyMusicRecommendations($sourceEmotion, $targetEmotion, 10);

    if (!empty($spotifyRecommendations)) {
        // Save to database for future use
        saveSpotifyMusicRecommendations($spotifyRecommendations);

        // Format for display
        foreach ($spotifyRecommendations as $rec) {
            $musicRecommendations[] = [
                'title' => $rec['title'],
                'description' => $rec['description'],
                'content' => $rec['content'],
                'image_url' => $rec['image_url'],
                'link' => $rec['link'],
                'preview_url' => $rec['preview_url'],
                'artist' => $rec['artist'],
                'album' => $rec['album']
            ];
        }
    }
}

// Include header
include '../includes/header.php';
?>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card shadow-lg">
                <div class="card-body p-5">
                    <h2 class="text-center mb-4">Music Recommendations</h2>

                    <div class="mood-transition-info text-center mb-4">
                        <p class="lead">
                            Music to help you transition from
                            <span class="badge bg-primary"><?php echo ucfirst($sourceEmotion); ?></span>
                            to
                            <span class="badge bg-success"><?php echo ucfirst($targetEmotion); ?></span>
                        </p>
                    </div>

                    <?php if (empty($musicRecommendations)): ?>
                        <div class="alert alert-info text-center">
                            <i class="fas fa-info-circle me-2"></i>
                            No music recommendations found for this mood transition. Try a different mood combination.
                        </div>
                    <?php else: ?>
                        <div class="music-recommendations">
                            <div class="row g-4">
                                <?php foreach ($musicRecommendations as $music): ?>
                                    <div class="col-md-6">
                                        <div class="card h-100 music-card">
                                            <div class="row g-0">
                                                <div class="col-md-4">
                                                    <img src="<?php echo $music['image_url'] ?: '../assets/images/recommendations/music_default.jpg'; ?>"
                                                         class="img-fluid rounded-start h-100 w-100 object-fit-cover"
                                                         alt="<?php echo htmlspecialchars($music['title']); ?>">
                                                </div>
                                                <div class="col-md-8">
                                                    <div class="card-body">
                                                        <h5 class="card-title"><?php echo htmlspecialchars($music['title']); ?></h5>
                                                        <p class="card-text"><?php echo htmlspecialchars($music['description']); ?></p>

                                                        <?php if (!empty($music['preview_url'])): ?>
                                                            <div class="music-preview mb-3">
                                                                <audio controls class="w-100">
                                                                    <source src="<?php echo $music['preview_url']; ?>" type="audio/mpeg">
                                                                    Your browser does not support the audio element.
                                                                </audio>
                                                            </div>
                                                        <?php endif; ?>

                                                        <a href="<?php echo $music['link']; ?>" target="_blank" class="btn btn-success w-100">
                                                            <i class="fab fa-spotify me-2"></i> Open in Spotify
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <div class="text-center mt-5">
                        <a href="<?php echo APP_URL; ?>/pages/mood_options.php?source=<?php echo $sourceEmotion; ?>&target=<?php echo $targetEmotion; ?>&emotion_id=<?php echo $emotionId; ?>" class="btn btn-outline-primary">
                            <i class="fas fa-arrow-left me-2"></i> Back to Options
                        </a>
                        <a href="<?php echo APP_URL; ?>" class="btn btn-outline-secondary ms-2">
                            <i class="fas fa-redo me-2"></i> New Mood Check
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.music-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border-radius: 15px;
    overflow: hidden;
    border: none;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.music-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0,0,0,0.15);
}

.mood-transition-info {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 30px;
}

.badge {
    font-size: 1rem;
    padding: 8px 15px;
}

audio {
    border-radius: 30px;
    height: 40px;
}

.object-fit-cover {
    object-fit: cover;
}
</style>

<?php include '../includes/footer.php'; ?>
