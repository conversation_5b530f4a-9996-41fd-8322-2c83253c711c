<header class="header">
    <div class="logo-container">
        <a href="<?php echo BASE_URL; ?>" class="logo">
            <img src="<?php echo BASE_URL; ?>/assets/images/logo.png" alt="Logo" width="40" height="40">
            <span>EmotionRec</span>
        </a>
    </div>
    
    <div class="search-box">
        <input type="text" placeholder="Search..." class="search-input">
        <button class="search-button">
            <i class="fas fa-search"></i>
        </button>
    </div>
    
    <div class="nav-buttons">
        <?php if(isset($_SESSION['user_id'])): ?>
            <a href="<?php echo BASE_URL; ?>/pages/emotion-detection.php" class="nav-button">
                <i class="fas fa-smile"></i>
                <span>Detect Mood</span>
            </a>
            <a href="<?php echo BASE_URL; ?>/pages/recommendations.php" class="nav-button">
                <i class="fas fa-film"></i>
                <span>Recommendations</span>
            </a>
            <a href="<?php echo BASE_URL; ?>/pages/profile.php" class="nav-button">
                <i class="fas fa-user"></i>
                <span>Profile</span>
            </a>
            <a href="<?php echo BASE_URL; ?>/api/user.php?action=logout" class="nav-button">
                <i class="fas fa-sign-out-alt"></i>
                <span>Logout</span>
            </a>
        <?php else: ?>
            <a href="<?php echo BASE_URL; ?>/pages/login.php" class="nav-button">
                <i class="fas fa-sign-in-alt"></i>
                <span>Login</span>
            </a>
            <a href="<?php echo BASE_URL; ?>/pages/register.php" class="nav-button">
                <i class="fas fa-user-plus"></i>
                <span>Register</span>
            </a>
        <?php endif; ?>
    </div>
    
    <button class="menu-toggle">
        <i class="fas fa-bars"></i>
    </button>
</header>

<div class="mobile-menu">
    <div class="mobile-menu-header">
        <a href="<?php echo BASE_URL; ?>" class="logo">
            <img src="<?php echo BASE_URL; ?>/assets/images/logo.png" alt="Logo" width="30" height="30">
            <span>EmotionRec</span>
        </a>
        <button class="close-menu">
            <i class="fas fa-times"></i>
        </button>
    </div>
    
    <div class="mobile-menu-items">
        <?php if(isset($_SESSION['user_id'])): ?>
            <a href="<?php echo BASE_URL; ?>/pages/emotion-detection.php" class="mobile-menu-item">
                <i class="fas fa-smile"></i>
                <span>Detect Mood</span>
            </a>
            <a href="<?php echo BASE_URL; ?>/pages/recommendations.php" class="mobile-menu-item">
                <i class="fas fa-film"></i>
                <span>Recommendations</span>
            </a>
            <a href="<?php echo BASE_URL; ?>/pages/profile.php" class="mobile-menu-item">
                <i class="fas fa-user"></i>
                <span>Profile</span>
            </a>
            <a href="<?php echo BASE_URL; ?>/api/user.php?action=logout" class="mobile-menu-item">
                <i class="fas fa-sign-out-alt"></i>
                <span>Logout</span>
            </a>
        <?php else: ?>
            <a href="<?php echo BASE_URL; ?>/pages/login.php" class="mobile-menu-item">
                <i class="fas fa-sign-in-alt"></i>
                <span>Login</span>
            </a>
            <a href="<?php echo BASE_URL; ?>/pages/register.php" class="mobile-menu-item">
                <i class="fas fa-user-plus"></i>
                <span>Register</span>
            </a>
        <?php endif; ?>
    </div>
</div>
