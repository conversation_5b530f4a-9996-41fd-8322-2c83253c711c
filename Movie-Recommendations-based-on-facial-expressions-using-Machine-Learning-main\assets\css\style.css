/* Main Stylesheet */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #3498db;
    --secondary-color: #2ecc71;
    --accent-color: #e74c3c;
    --text-color: #333;
    --light-text: #f5f5f5;
    --dark-bg: #2c3e50;
    --light-bg: #ecf0f1;
    --border-color: #ddd;
    --shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--light-bg);
}

a {
    text-decoration: none;
    color: var(--primary-color);
    transition: var(--transition);
}

a:hover {
    color: #217dbb;
}

ul {
    list-style: none;
}

img {
    max-width: 100%;
}

/* Button Styles */
.btn {
    display: inline-block;
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-weight: 600;
    transition: var(--transition);
    text-align: center;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.primary-btn {
    background-color: var(--primary-color);
    color: white;
}

.primary-btn:hover:not(:disabled) {
    background-color: #217dbb;
}

.secondary-btn {
    background-color: var(--secondary-color);
    color: white;
}

.secondary-btn:hover:not(:disabled) {
    background-color: #25a25a;
}

.accent-btn {
    background-color: var(--accent-color);
    color: white;
}

.accent-btn:hover:not(:disabled) {
    background-color: #c0392b;
}

.outline-btn {
    background-color: transparent;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
}

.outline-btn:hover:not(:disabled) {
    background-color: var(--primary-color);
    color: white;
}

/* Layout */
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

main {
    display: flex;
    min-height: calc(100vh - 160px); /* Adjust based on header and footer height */
}

/* Header Styles */
.header {
    background-color: var(--dark-bg);
    color: var(--light-text);
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: var(--shadow);
    position: sticky;
    top: 0;
    z-index: 100;
}

.logo-container {
    display: flex;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    color: var(--light-text);
    font-weight: 700;
    font-size: 1.2rem;
}

.logo img {
    margin-right: 10px;
}

.search-box {
    flex: 0 1 400px;
    display: flex;
    margin: 0 20px;
}

.search-input {
    flex: 1;
    padding: 8px 15px;
    border: none;
    border-radius: 20px 0 0 20px;
    outline: none;
}

.search-button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 0 20px 20px 0;
    padding: 8px 15px;
    cursor: pointer;
}

.nav-buttons {
    display: flex;
    gap: 10px;
}

.nav-button {
    color: var(--light-text);
    padding: 8px 15px;
    border-radius: 5px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.nav-button:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--light-text);
}

.menu-toggle {
    display: none;
    background: none;
    border: none;
    color: var(--light-text);
    font-size: 1.5rem;
    cursor: pointer;
}

/* Sidebar Styles */
.sidebar {
    width: 250px;
    background-color: white;
    box-shadow: var(--shadow);
    padding: 20px;
    flex-shrink: 0;
}

.sidebar-header {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--border-color);
}

.sidebar-menu {
    margin-bottom: 30px;
}

.sidebar-item {
    margin-bottom: 5px;
}

.sidebar-link {
    display: flex;
    align-items: center;
    padding: 10px;
    color: var(--text-color);
    border-radius: 5px;
    transition: var(--transition);
}

.sidebar-link i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

.sidebar-link:hover {
    background-color: var(--light-bg);
}

/* Footer Styles */
.footer {
    background-color: var(--dark-bg);
    color: var(--light-text);
    padding: 40px 20px 20px;
}

.footer-content {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    max-width: 1200px;
    margin: 0 auto;
    gap: 30px;
}

.footer-section {
    flex: 1 1 300px;
}

.footer-section h3 {
    margin-bottom: 20px;
    position: relative;
    padding-bottom: 10px;
}

.footer-section h3::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    width: 50px;
    height: 2px;
    background-color: var(--primary-color);
}

.footer-section p {
    margin-bottom: 20px;
}

.contact span {
    display: block;
    margin-bottom: 10px;
}

.socials {
    margin-top: 20px;
}

.socials a {
    display: inline-block;
    margin-right: 15px;
    color: var(--light-text);
    font-size: 1.2rem;
}

.footer-section.links ul li {
    margin-bottom: 10px;
}

.footer-section.links ul li a {
    color: var(--light-text);
}

.footer-section.links ul li a:hover {
    color: var(--primary-color);
    margin-left: 5px;
}

.contact-input {
    display: block;
    width: 100%;
    padding: 10px;
    margin-bottom: 10px;
    border: none;
    border-radius: 5px;
    outline: none;
}

.contact-btn {
    width: 100%;
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    margin-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Mobile Menu */
.mobile-menu {
    position: fixed;
    top: 0;
    left: -100%;
    width: 80%;
    max-width: 300px;
    height: 100vh;
    background-color: white;
    z-index: 1000;
    transition: left 0.3s ease;
    box-shadow: var(--shadow);
    overflow-y: auto;
}

.mobile-menu.active {
    left: 0;
}

.mobile-menu-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background-color: var(--dark-bg);
    color: var(--light-text);
}

.close-menu {
    background: none;
    border: none;
    color: var(--light-text);
    font-size: 1.2rem;
    cursor: pointer;
}

.mobile-menu-items {
    padding: 15px;
}

.mobile-menu-item {
    display: flex;
    align-items: center;
    padding: 15px;
    color: var(--text-color);
    border-bottom: 1px solid var(--border-color);
}

.mobile-menu-item i {
    margin-right: 15px;
    width: 20px;
    text-align: center;
}

/* Hero Section */
.hero-section {
    text-align: center;
    padding: 60px 20px;
    background-color: white;
    border-radius: 10px;
    box-shadow: var(--shadow);
    margin-bottom: 40px;
}

.hero-section h1 {
    font-size: 2.5rem;
    margin-bottom: 20px;
    color: var(--dark-bg);
}

.hero-section p {
    font-size: 1.2rem;
    margin-bottom: 30px;
    color: #666;
}

.action-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

/* Features Section */
.features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.feature-card {
    background-color: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: var(--shadow);
    text-align: center;
    transition: var(--transition);
}

.feature-card:hover {
    transform: translateY(-5px);
}

.feature-icon {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 20px;
}

.feature-card h3 {
    margin-bottom: 15px;
    color: var(--dark-bg);
}

/* Responsive Styles */
@media (max-width: 992px) {
    .nav-buttons {
        display: none;
    }
    
    .menu-toggle {
        display: block;
    }
    
    main {
        flex-direction: column;
    }
    
    .sidebar {
        width: 100%;
        margin-bottom: 20px;
    }
}

@media (max-width: 768px) {
    .search-box {
        display: none;
    }
    
    .hero-section h1 {
        font-size: 2rem;
    }
    
    .footer-content {
        flex-direction: column;
    }
}

@media (max-width: 576px) {
    .hero-section {
        padding: 40px 15px;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .features {
        grid-template-columns: 1fr;
    }
}
