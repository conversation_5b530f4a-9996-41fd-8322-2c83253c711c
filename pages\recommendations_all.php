<?php
/**
 * MoodifyMe - All Recommendations Page
 */

// Include configuration and functions
require_once '../config.php';
require_once '../includes/functions.php';
require_once '../includes/db_connect.php';
require_once '../includes/tmdb_api.php'; // Include TMDB API functions
require_once '../includes/recommendation_functions.php'; // Include recommendation functions

// Start session
session_start();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    // Redirect to login page
    redirect(APP_URL . '/pages/login.php');
}

// Get user ID
$userId = $_SESSION['user_id'];

// Get source and target emotions from URL parameters
$sourceEmotion = isset($_GET['source']) ? sanitizeInput($_GET['source']) : '';
$targetEmotion = isset($_GET['target']) ? sanitizeInput($_GET['target']) : '';
$specificType = isset($_GET['type']) ? sanitizeInput($_GET['type']) : '';

// If source or target emotion is not provided, redirect to home page
if (empty($sourceEmotion) || empty($targetEmotion)) {
    // Check if user has recent emotions
    $stmt = $conn->prepare("SELECT emotion_type FROM emotions WHERE user_id = ? ORDER BY created_at DESC LIMIT 1");
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 1) {
        $row = $result->fetch_assoc();
        $sourceEmotion = $row['emotion_type'];
        $targetEmotion = 'happy'; // Default target emotion
    } else {
        // Redirect to home page
        redirect(APP_URL);
    }
}

// Get recommendations
if (!empty($specificType) && array_key_exists($specificType, REC_TYPES)) {
    // If a specific type is requested, only get recommendations of that type
    $recommendations = getRecommendationsByType($specificType, $sourceEmotion, $targetEmotion, 10);
} else {
    // Otherwise get all recommendations
    $recommendations = getRecommendations($sourceEmotion, $targetEmotion);
}

// Include header
include '../includes/header.php';
?>

<div class="container recommendations-container">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-body">
                    <h2 class="card-title">
                        <?php if (!empty($specificType) && array_key_exists($specificType, REC_TYPES)): ?>
                            <?php echo REC_TYPES[$specificType]; ?> Recommendations for You
                        <?php else: ?>
                            Recommendations for You
                        <?php endif; ?>
                    </h2>
                    <p class="card-text">
                        Based on your current mood (<strong><?php echo ucfirst($sourceEmotion); ?></strong>),
                        here are <?php echo !empty($specificType) && array_key_exists($specificType, REC_TYPES) ? strtolower(REC_TYPES[$specificType]) : 'recommendations'; ?>
                        to help you feel <strong><?php echo ucfirst($targetEmotion); ?></strong>.
                    </p>

                    <div class="recommendation-filters mb-3">
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-primary recommendation-filter <?php echo empty($specificType) ? 'active' : ''; ?>" data-filter="all">All</button>
                            <?php foreach (REC_TYPES as $type => $label): ?>
                                <button type="button" class="btn btn-outline-primary recommendation-filter <?php echo $specificType === $type ? 'active' : ''; ?>" data-filter="<?php echo $type; ?>">
                                    <?php echo $label; ?>
                                </button>
                            <?php endforeach; ?>
                        </div>
                    </div>

                    <!-- TMDB Movie Recommendations Button -->
                    <div class="tmdb-recommendations mt-3">
                        <a href="<?php echo APP_URL; ?>/pages/movie_genres.php?source=<?php echo $sourceEmotion; ?>&target=<?php echo $targetEmotion; ?>" class="btn btn-primary">
                            <i class="fas fa-film"></i> Browse Movie Genres for Your Mood
                        </a>
                        <p class="text-muted small mt-2">
                            Explore movie genres that can help you transition from <strong><?php echo ucfirst($sourceEmotion); ?></strong>
                            to <strong><?php echo ucfirst($targetEmotion); ?></strong> mood.
                        </p>
                    </div>

                    <!-- African Meals Recommendations -->
                    <div class="african-meals-recommendations mt-4">
                        <h4><i class="fas fa-utensils"></i> African Meals for Your Mood</h4>
                        <p>
                            Discover traditional African dishes that can help enhance your mood transition from
                            <strong><?php echo ucfirst($sourceEmotion); ?></strong> to <strong><?php echo ucfirst($targetEmotion); ?></strong>.
                        </p>
                        <?php
                        // Get African meals recommendations
                        $africanMealsRecommendations = getRecommendationsByType('african_meals', $sourceEmotion, $targetEmotion, 3);

                        if (!empty($africanMealsRecommendations)) {
                            echo '<div class="row">';
                            foreach ($africanMealsRecommendations as $meal) {
                                ?>
                                <div class="col-md-4 mb-3">
                                    <div class="card h-100">
                                        <img src="<?php echo $meal['image_url']; ?>" class="card-img-top" alt="<?php echo htmlspecialchars($meal['title']); ?>" onerror="this.src='<?php echo APP_URL; ?>/assets/images/placeholder-meal.jpg'">
                                        <div class="card-body">
                                            <h5 class="card-title"><?php echo htmlspecialchars($meal['title']); ?></h5>
                                            <p class="card-text"><?php echo htmlspecialchars($meal['description']); ?></p>
                                            <p class="card-text small text-muted"><?php echo htmlspecialchars($meal['content']); ?></p>
                                        </div>
                                        <div class="card-footer">
                                            <a href="<?php echo $meal['link']; ?>" class="btn btn-sm btn-outline-primary" target="_blank">View Recipe</a>
                                        </div>
                                    </div>
                                </div>
                                <?php
                            }
                            echo '</div>';
                        } else {
                            echo '<div class="alert alert-info">No African meal recommendations available for this mood transition. Try another mood combination.</div>';
                        }
                        ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row recommendations-grid">
        <?php if (empty($recommendations)): ?>
            <div class="col-md-12">
                <div class="alert alert-info">
                    <p>No recommendations found for this mood transition. Try a different target mood or check back later.</p>
                </div>
            </div>
        <?php else: ?>
            <?php foreach ($recommendations as $recommendation): ?>
                <div class="col-md-4 mb-4">
                    <div class="card recommendation-card" data-type="<?php echo $recommendation['type']; ?>">
                        <?php if (!empty($recommendation['image_url'])): ?>
                            <div class="card-img-top" style="background-image: url('<?php echo $recommendation['image_url']; ?>')"></div>
                        <?php endif; ?>
                        <div class="card-body">
                            <h5 class="card-title"><?php echo $recommendation['title']; ?></h5>
                            <p class="card-text"><?php echo $recommendation['description']; ?></p>
                            <div class="recommendation-meta">
                                <span class="badge bg-primary"><?php echo ucfirst($recommendation['type']); ?></span>
                                <div class="recommendation-actions">
                                    <?php
                                    // Check if user has already liked or disliked this recommendation
                                    $stmt = $conn->prepare("SELECT feedback_type FROM recommendation_feedback WHERE user_id = ? AND recommendation_id = ?");
                                    $stmt->bind_param("ii", $userId, $recommendation['id']);
                                    $stmt->execute();
                                    $feedbackResult = $stmt->get_result();
                                    $userFeedback = $feedbackResult->num_rows > 0 ? $feedbackResult->fetch_assoc()['feedback_type'] : '';
                                    ?>
                                    <button class="btn btn-sm btn-outline-success recommendation-like <?php echo $userFeedback === 'like' ? 'active' : ''; ?>" data-id="<?php echo $recommendation['id']; ?>">
                                        <i class="fas fa-thumbs-up"></i> <span><?php echo $recommendation['likes']; ?></span>
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger recommendation-dislike <?php echo $userFeedback === 'dislike' ? 'active' : ''; ?>" data-id="<?php echo $recommendation['id']; ?>">
                                        <i class="fas fa-thumbs-down"></i> <span><?php echo $recommendation['dislikes']; ?></span>
                                    </button>
                                </div>
                            </div>
                            <?php if (!empty($recommendation['link'])): ?>
                                <div class="mt-2">
                                    <a href="<?php echo $recommendation['link']; ?>" class="btn btn-sm btn-outline-primary" target="_blank">
                                        <i class="fas fa-external-link-alt"></i> View
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>

    <?php if (count($recommendations) >= 6): ?>
        <div class="row">
            <div class="col-md-12 text-center mt-3 mb-5">
                <button id="load-more-recommendations" class="btn btn-outline-primary">Load More</button>
            </div>
        </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-body">
                    <h3>Not finding what you're looking for?</h3>
                    <p>Try a different mood target or go back to check your mood again.</p>
                    <div class="d-flex gap-2">
                        <a href="<?php echo APP_URL; ?>" class="btn btn-primary">
                            <i class="fas fa-redo"></i> New Mood Check
                        </a>
                        <a href="<?php echo APP_URL; ?>/pages/recommendations.php?source=<?php echo $sourceEmotion; ?>&target=<?php echo $targetEmotion; ?>&emotion_id=<?php echo isset($_GET['emotion_id']) ? $_GET['emotion_id'] : ''; ?>" class="btn btn-outline-primary">
                            <i class="fas fa-arrow-left"></i> Back to Mood Options
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
include '../includes/footer.php';
?>

<style>
.tmdb-recommendations {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    border-left: 4px solid #007bff;
}

.tmdb-recommendations .btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.tmdb-recommendations .btn i {
    font-size: 1.2em;
}

.tmdb-recommendations p {
    margin-bottom: 0;
}
</style>
