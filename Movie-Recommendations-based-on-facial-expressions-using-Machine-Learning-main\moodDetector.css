@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap');

* {
    font-family: 'Poppins', sans-serif;
    margin: 0;
    padding: 0;
    scroll-behavior: smooth;
    box-sizing: border-box;
}

body {
    width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center; /* Center align horizontally */
    color: white;
    background: black;
    overflow: hidden; /* Prevent scrollbars */
}

video {
    width: 100%; /* Full width */
    height: auto; /* Maintain aspect ratio */
    max-height: 80vh; /* Limit height to prevent overflow */
    border-radius: 0.5rem;
}

button {
    margin-top: 20px; /* Space between video and button */
    padding: 10px 20px;
    font-size: 1rem;
    font-weight: 500;
    color: black;
    background: white;
    border: none;
    border-radius: 0.4rem;
    cursor: pointer;
}

button:hover {
    background: hsl(181, 100%, 44%, 0.8);
    transition: 0.3s all linear;
}

canvas {
    display: block;
    margin: 0 auto;
    position: absolute; /* Ensure it overlays correctly */
}
