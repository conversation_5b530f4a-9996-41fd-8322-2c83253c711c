<?php
/**
 * Database Configuration
 * 
 * This file contains the database connection settings.
 */

// Database credentials
define('DB_HOST', 'localhost');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_NAME', 'emotion_recommendation_system');

// Create connection
$conn = new mysqli(DB_HOST, DB_USER, DB_PASS);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Create database if it doesn't exist
$sql = "CREATE DATABASE IF NOT EXISTS " . DB_NAME;
if ($conn->query($sql) !== TRUE) {
    die("Error creating database: " . $conn->error);
}

// Select the database
$conn->select_db(DB_NAME);

// Create tables if they don't exist
$tables = [
    "CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) NOT NULL UNIQUE,
        email VARCHAR(100) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )",
    
    "CREATE TABLE IF NOT EXISTS emotions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(50) NOT NULL UNIQUE,
        description TEXT,
        recommendation_categories TEXT
    )",
    
    "CREATE TABLE IF NOT EXISTS user_emotions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT,
        emotion_id INT,
        intensity FLOAT,
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id),
        FOREIGN KEY (emotion_id) REFERENCES emotions(id)
    )",
    
    "CREATE TABLE IF NOT EXISTS recommendations (
        id INT AUTO_INCREMENT PRIMARY KEY,
        type ENUM('movie', 'music', 'meal', 'joke') NOT NULL,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        image_url VARCHAR(255),
        external_id VARCHAR(100),
        emotion_tags TEXT
    )",
    
    "CREATE TABLE IF NOT EXISTS user_recommendations (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT,
        recommendation_id INT,
        source_emotion_id INT,
        target_emotion_id INT,
        rating INT,
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id),
        FOREIGN KEY (recommendation_id) REFERENCES recommendations(id),
        FOREIGN KEY (source_emotion_id) REFERENCES emotions(id),
        FOREIGN KEY (target_emotion_id) REFERENCES emotions(id)
    )"
];

// Execute each table creation query
foreach ($tables as $table) {
    if ($conn->query($table) !== TRUE) {
        die("Error creating table: " . $conn->error);
    }
}

// Insert default emotions if they don't exist
$emotions = [
    ['happy', 'Feeling or showing pleasure or contentment', 'comedy,adventure,animation'],
    ['sad', 'Feeling or showing sorrow; unhappy', 'drama,romance,documentary'],
    ['angry', 'Feeling or showing strong annoyance, displeasure, or hostility', 'action,thriller,crime'],
    ['fearful', 'Feeling afraid; showing fear or anxiety', 'horror,thriller,mystery'],
    ['surprised', 'Feeling or showing surprise', 'sci-fi,fantasy,mystery'],
    ['disgusted', 'Feeling or showing strong dislike or disapproval', 'documentary,comedy,drama'],
    ['neutral', 'Having no strongly marked or positive characteristics or features', 'documentary,drama,comedy']
];

// Prepare statement for inserting emotions
$stmt = $conn->prepare("INSERT IGNORE INTO emotions (name, description, recommendation_categories) VALUES (?, ?, ?)");
$stmt->bind_param("sss", $name, $description, $categories);

// Insert each emotion
foreach ($emotions as $emotion) {
    $name = $emotion[0];
    $description = $emotion[1];
    $categories = $emotion[2];
    $stmt->execute();
}

$stmt->close();
$conn->close();
?>
