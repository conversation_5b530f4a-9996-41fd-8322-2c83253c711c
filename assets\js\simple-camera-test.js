/**
 * Simple Camera Test - Just test camera access without face-api
 */

// Simple camera test function
async function testCameraOnly() {
    console.log('🧪 Testing camera access only...');
    
    const video = document.getElementById('face-video');
    const emotionResult = document.getElementById('face-result');
    
    if (!video) {
        console.error('❌ Video element not found');
        return;
    }
    
    try {
        console.log('📹 Requesting camera...');
        
        if (emotionResult) {
            emotionResult.innerHTML = `
                <div class="alert alert-info">
                    <p><i class="fas fa-spinner fa-spin"></i> Testing camera access...</p>
                </div>
            `;
        }
        
        const stream = await navigator.mediaDevices.getUserMedia({ 
            video: { 
                width: { ideal: 640 }, 
                height: { ideal: 480 }
            } 
        });
        
        console.log('✅ Camera stream obtained');
        video.srcObject = stream;
        await video.play();
        console.log('▶️ Video playing');
        
        if (emotionResult) {
            emotionResult.innerHTML = `
                <div class="alert alert-success">
                    <p><i class="fas fa-check-circle"></i> Camera test successful!</p>
                    <p>Camera is working. You should see your video feed above.</p>
                </div>
            `;
        }
        
    } catch (error) {
        console.error('❌ Camera test failed:', error);
        
        if (emotionResult) {
            emotionResult.innerHTML = `
                <div class="alert alert-danger">
                    <p><i class="fas fa-exclamation-triangle"></i> Camera test failed!</p>
                    <p>Error: ${error.message}</p>
                </div>
            `;
        }
    }
}

// Make it globally available
window.testCameraOnly = testCameraOnly;

console.log('📷 Simple camera test script loaded');
