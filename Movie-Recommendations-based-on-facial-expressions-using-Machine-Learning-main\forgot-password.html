<!DOCTYPE html>
<html>

<head>
  <title>Forgot Password</title>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <link rel="stylesheet" href="forgot-password.css">
</head>
<script>
  window.addEventListener("load", () => {
            let loader = document.getElementById("preloader");
            setTimeout(function () {
                loader.style.display = "none";
            }, 1500);
        });
  let blob = document.getElementById("blob");
        window.onpointermove = event => {
            const { clientX, clientY } = event;
            blob.animate({
                left: `${clientX}px`,
                top: `${clientY}px`
            }, { duration: 2500, fill: "forwards" });
        }
</script>

<body>
  <div id="preloader"></div>
  <div class="blob" id="blob"></div>
  <h1>Forgot Password</h1>
  <form action="#" method="post" class="form-box">
    <label for="email" >Email</label>
    <input type="email" id="email" name="email" required class="input"/>

    <button type="submit" class="span1">Submit</button>
  </form>
  
  <div class="head">
    <nav>
      <a href="UniFlix.html">
        <img class="icon" src="icons/icon.png">
      </a>
      <ul class="ul1">
        <li class="li1">
          <a class="a1" href="premium.html">
            Premium
          </a>
        </li>
        <li class="li1">
          <a class="a1" href="download.html">
            Download
          </a>
        </li>
        <li class="li1">
          <a class="a1" href="aboutus.html">
            About us
          </a>
        </li>
        <li class="li1">
          <a class="a1" href="support.html">
            Support
          </a>
        </li>
      </ul>
    </nav>
    <div class="smedia">
      <ul class="ul2">
          <li class="li2">
              <a class="a2" href="https://twitter.com" target="_blank">
                  <span class="span2">
                      <i class="fa-brands fa-twitter"></i>
                  </span>
              </a>
          </li>
          <li class="li2">
              <a class="a2" href="https://facebook.com" target="_blank">
                  <span class="span2">
                      <i id="smico" class="fa-brands fa-facebook-f"></i>
                  </span>
              </a>
          </li>
          <li class="li2">
              <a class="a2" href="https://linkedin.com" target="_blank">
                  <span class="span2">
                      <i id="smico" class="fa-brands fa-linkedin-in"></i>
                  </span>
              </a>
          </li>
          <li class="li2">
              <a class="a2" href="https://instagram.com" target="_blank">
                  <span class="span2">
                      <i id="smico" class="fa-brands fa-instagram"></i>
                  </span>
              </a>
          </li>
          <li class="li2">
              <a class="a2" href="https://youtube.com" target="_blank">
                  <span class="span2">
                      <i id="smico" class="fa-brands fa-youtube"></i>
                  </span>
              </a>
          </li>
      </ul>
  </div>
</div>
    <script>
      const emailInput = document.getElementById("email");

      emailInput.addEventListener("input", (event) => {
        const email = event.target.value;
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

        if (!emailRegex.test(email)) {
          emailInput.setCustomValidity("Invalid email address");
        } else {
          emailInput.setCustomValidity("");
        }
      });
    </script>

    <div id="landing-page" style="display: none;">
      <h1>Change Password</h1>
      <form action="#" method="post" class="form-box">
        <label for="old-password" >Old Password</label>
        <input type="password" id="old-password" name="old-password" required class="input" />

        <label for="new-password" >New Password</label>
        <input type="password" id="new-password" name="new-password" required class="input"/>

        <label for="reenter-password">Re-enter New Password</label>
        <input type="password" id="reenter-password" name="reenter-password" required class="input"/>

        <button type="submit" class="span1">Submit</button>
      </form>
    </div>

    <script>
      const landingPage = document.getElementById("landing-page");

      const form = document.querySelector("form");
      form.addEventListener("submit", (event) => {
        event.preventDefault();
        const email = document.getElementById("email").value;
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

        if (emailRegex.test(email)) {
          form.style.display = "none";
          landingPage.style.display = "block";
        } else {
          document.getElementById("email").setCustomValidity("Invalid email address");
        }
      });
    </script>
</body>

</html>