@font-face {
    font-family: main;
    src: url(fonts/main.otf);
}

* {
    margin: 0px;
    padding: 0px;
    box-sizing: border-box;
    font-family: "main";
    background-color: black;
    color: white;
}

div {
    position: absolute;
    top: 50%;
    left: 50%;
    margin-right: -50%;
    transform: translate(-50%, -50%);
}

nav {
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    padding: 1.25%;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.icon {
    width: 2.75vw;
    height: 2.75vw;
}

nav .ul1 .li1 {
    list-style: none;
    display: inline-block;
    margin-left: 2.5vw;
    margin-right: 0.75vw;
}

.a1 {
    text-decoration: none;
    position: relative;
    padding-bottom: 0.75vw;
    transition: 0.25s;
    color: white;
}

.a1::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 0.25vw;
    border-radius: 1vw;
    background-image: radial-gradient(circle farthest-corner at 92.3% 71.5%, rgba(83, 138, 214, 1) 0%, rgba(134, 231, 214, 1) 90%);
    bottom: 0;
    left: 0;
    transform-origin: right;
    transform: scaleX(0);
    background-size: 200%;
    animation: flow 2.5s ease-in-out infinite;
    transition: transform 0.25s ease-in-out;
}

.a1:hover::before {
    transform-origin: left;
    transform: scaleX(1);
}

button {
    background-color: white;
    padding: 5px 10px;
    color: black;
    border: none;
    border-radius: 10px;
}