<?php
/**
 * Emotion API
 * 
 * Handles emotion detection and processing requests.
 */

// Initialize session and include required files
session_start();
require_once '../config/config.php';
require_once '../config/database.php';
require_once '../classes/Database.php';
require_once '../classes/Emotion.php';

// Set content type to JSON
header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode([
        'success' => false,
        'message' => 'User not authenticated'
    ]);
    exit;
}

$userId = $_SESSION['user_id'];
$emotionObj = new Emotion();

// Get the action from the request
$action = isset($_GET['action']) ? $_GET['action'] : '';

switch ($action) {
    case 'detect-facial':
        // Process facial emotion detection
        detectFacialEmotion();
        break;
        
    case 'detect-text':
        // Process text-based emotion detection
        detectTextEmotion();
        break;
        
    case 'record':
        // Record user emotion
        recordEmotion();
        break;
        
    case 'get-latest':
        // Get user's latest emotion
        getLatestEmotion();
        break;
        
    default:
        // Invalid action
        echo json_encode([
            'success' => false,
            'message' => 'Invalid action'
        ]);
        break;
}

/**
 * Process facial emotion data
 */
function detectFacialEmotion() {
    global $emotionObj;
    
    // Get emotion data from POST request
    $data = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($data['emotions'])) {
        echo json_encode([
            'success' => false,
            'message' => 'No emotion data provided'
        ]);
        return;
    }
    
    // Process emotion data
    $dominantEmotion = $emotionObj->processFacialEmotion($data['emotions']);
    
    echo json_encode([
        'success' => true,
        'emotion' => $dominantEmotion,
        'confidence' => $data['emotions'][$dominantEmotion] ?? 0
    ]);
}

/**
 * Process text-based emotion detection
 */
function detectTextEmotion() {
    global $emotionObj;
    
    // Get text from POST request
    $data = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($data['text']) || empty($data['text'])) {
        echo json_encode([
            'success' => false,
            'message' => 'No text provided'
        ]);
        return;
    }
    
    // In a real application, this would use NLP or a sentiment analysis API
    // For this example, we'll use a simple keyword-based approach
    $emotionKeywords = [
        'happy' => ['happy', 'joy', 'excited', 'great', 'wonderful', 'pleased', 'delighted'],
        'sad' => ['sad', 'unhappy', 'depressed', 'down', 'miserable', 'gloomy', 'heartbroken'],
        'angry' => ['angry', 'mad', 'furious', 'annoyed', 'irritated', 'frustrated', 'enraged'],
        'fearful' => ['afraid', 'scared', 'fearful', 'terrified', 'anxious', 'worried', 'nervous'],
        'surprised' => ['surprised', 'shocked', 'amazed', 'astonished', 'stunned', 'unexpected'],
        'disgusted' => ['disgusted', 'revolted', 'repulsed', 'sickened', 'appalled', 'horrified'],
        'neutral' => ['okay', 'fine', 'neutral', 'normal', 'average', 'indifferent']
    ];
    
    // Count occurrences of emotion keywords
    $emotionCounts = [];
    $words = explode(' ', strtolower($data['text']));
    
    foreach ($emotionKeywords as $emotion => $keywords) {
        $emotionCounts[$emotion] = 0;
        
        foreach ($words as $word) {
            $word = preg_replace('/[^\w]/', '', $word);
            if (in_array($word, $keywords)) {
                $emotionCounts[$emotion]++;
            }
        }
    }
    
    // Determine dominant emotion
    $dominantEmotion = 'neutral';
    $maxCount = 0;
    
    foreach ($emotionCounts as $emotion => $count) {
        if ($count > $maxCount) {
            $maxCount = $count;
            $dominantEmotion = $emotion;
        }
    }
    
    echo json_encode([
        'success' => true,
        'emotion' => $dominantEmotion,
        'counts' => $emotionCounts
    ]);
}

/**
 * Record user emotion
 */
function recordEmotion() {
    global $emotionObj, $userId;
    
    // Get data from POST request
    $data = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($data['emotion'])) {
        echo json_encode([
            'success' => false,
            'message' => 'No emotion provided'
        ]);
        return;
    }
    
    // Get emotion ID
    $emotion = $emotionObj->getEmotionByName($data['emotion']);
    
    if (!$emotion) {
        echo json_encode([
            'success' => false,
            'message' => 'Invalid emotion'
        ]);
        return;
    }
    
    // Record emotion
    $intensity = isset($data['intensity']) ? floatval($data['intensity']) : 1.0;
    $result = $emotionObj->recordUserEmotion($userId, $emotion['id'], $intensity);
    
    if ($result) {
        echo json_encode([
            'success' => true,
            'message' => 'Emotion recorded successfully',
            'id' => $result
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Failed to record emotion'
        ]);
    }
}

/**
 * Get user's latest emotion
 */
function getLatestEmotion() {
    global $emotionObj, $userId;
    
    $latestEmotion = $emotionObj->getUserLatestEmotion($userId);
    
    if ($latestEmotion) {
        echo json_encode([
            'success' => true,
            'emotion' => $latestEmotion
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'No emotion records found'
        ]);
    }
}
?>
