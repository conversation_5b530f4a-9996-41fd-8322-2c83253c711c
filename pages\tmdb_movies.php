<?php
/**
 * MoodifyMe - TMDB Movie Recommendations
 * Shows movie recommendations from TMDB based on user's mood transition
 */

// Include necessary files
require_once '../config.php';
require_once '../includes/db_connect.php';
require_once '../includes/functions.php';
require_once '../includes/tmdb_api.php';

// Start session
session_start();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    // Redirect to login page
    redirect(APP_URL . '/pages/login.php');
}

// Get user ID
$userId = $_SESSION['user_id'];

// Get source and target emotions from URL parameters
$sourceEmotion = isset($_GET['source']) ? sanitizeInput($_GET['source']) : '';
$targetEmotion = isset($_GET['target']) ? sanitizeInput($_GET['target']) : '';
$genreId = isset($_GET['genre']) ? intval($_GET['genre']) : 0;

// If source or target emotion is not provided, redirect to home page
if (empty($sourceEmotion) || empty($targetEmotion)) {
    // Check if user has recent emotions
    $stmt = $conn->prepare("SELECT emotion_type FROM emotions WHERE user_id = ? ORDER BY created_at DESC LIMIT 1");
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 1) {
        $row = $result->fetch_assoc();
        $sourceEmotion = $row['emotion_type'];
        $targetEmotion = 'happy'; // Default target emotion
    } else {
        // Redirect to home page
        redirect(APP_URL);
    }
}

// Get movie recommendations from TMDB
if ($genreId > 0) {
    // If a specific genre is requested, get movies for that genre
    $movieRecommendations = getTMDBMoviesByGenre($genreId, 12);
} else {
    // Otherwise get recommendations based on emotion transition
    $movieRecommendations = getTMDBMovieRecommendations($sourceEmotion, $targetEmotion, 12);
}

// Handle saving recommendations to database
$message = '';
if (isset($_POST['save_recommendations']) && !empty($_POST['selected_movies'])) {
    $selectedMovies = json_decode($_POST['selected_movies'], true);
    $moviesToSave = [];

    foreach ($movieRecommendations as $movie) {
        if (in_array($movie['title'], $selectedMovies)) {
            $moviesToSave[] = $movie;
        }
    }

    if (saveTMDBMovieRecommendations($moviesToSave)) {
        $message = 'Selected movie recommendations have been saved to your personal collection!';
    } else {
        $message = 'Error saving recommendations. Please try again.';
    }
}

// Include header
include '../includes/header.php';
?>

<div class="movie-header py-5" style="background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center text-white">
                <h1 class="display-4 fw-bold mb-3 animate__animated animate__fadeInDown">Movie Recommendations</h1>
                <p class="lead mb-4 animate__animated animate__fadeIn" style="animation-delay: 0.2s">
                    Based on your <span class="emotion-badge <?php echo $sourceEmotion; ?>"><?php echo ucfirst($sourceEmotion); ?></span> mood,
                    here are movies to help you feel <span class="emotion-badge <?php echo $targetEmotion; ?>"><?php echo ucfirst($targetEmotion); ?></span>
                </p>

                <?php if (!empty($message)): ?>
                    <div class="alert alert-light animate__animated animate__fadeIn" style="animation-delay: 0.3s">
                        <?php echo $message; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    <div class="wave-bottom">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320">
            <path fill="#ffffff" fill-opacity="1" d="M0,96L48,112C96,128,192,160,288,160C384,160,480,128,576,122.7C672,117,768,139,864,149.3C960,160,1056,160,1152,138.7C1248,117,1344,75,1392,53.3L1440,32L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path>
        </svg>
    </div>
</div>

<div class="container mt-n5 mb-5 position-relative z-index-1">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow-lg border-0 animate__animated animate__fadeInUp">
                <div class="card-body p-4">
                    <div class="d-flex justify-content-between align-items-center flex-wrap">
                        <a href="<?php echo APP_URL; ?>/pages/recommendations.php?source=<?php echo $sourceEmotion; ?>&target=<?php echo $targetEmotion; ?>" class="btn btn-outline-primary mb-3 mb-md-0">
                            <i class="fas fa-arrow-left me-2"></i> Back to All Recommendations
                        </a>

                        <form method="post" id="save-recommendations-form">
                            <input type="hidden" name="selected_movies" id="selected-movies-input" value="">
                            <button type="button" id="save-selected-btn" class="btn btn-accent">
                                <i class="fas fa-save me-2"></i> Save Selected Movies
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <?php if (empty($movieRecommendations)): ?>
            <div class="col-md-12">
                <div class="alert alert-info">
                    <p>No movie recommendations found for this mood transition. Try a different target mood or check back later.</p>
                </div>
            </div>
        <?php else: ?>
            <?php $delay = 0; foreach ($movieRecommendations as $movie): $delay += 0.1; ?>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card h-100 movie-card animate__animated animate__fadeInUp" style="animation-delay: <?php echo $delay; ?>s">
                        <div class="card-header p-0 position-relative">
                            <?php if (!empty($movie['image_url'])): ?>
                                <img src="<?php echo $movie['image_url']; ?>" class="card-img-top" alt="<?php echo $movie['title']; ?>">
                                <div class="movie-overlay">
                                    <div class="movie-rating">
                                        <?php
                                            $rating = 0;
                                            if (preg_match('/Rating: ([\d\.]+)\/10/', $movie['content'], $matches)) {
                                                $rating = floatval($matches[1]);
                                            }
                                            $stars = round($rating / 2);
                                        ?>
                                        <div class="stars">
                                            <?php for ($i = 1; $i <= 5; $i++): ?>
                                                <?php if ($i <= $stars): ?>
                                                    <i class="fas fa-star"></i>
                                                <?php elseif ($i - 0.5 <= $stars): ?>
                                                    <i class="fas fa-star-half-alt"></i>
                                                <?php else: ?>
                                                    <i class="far fa-star"></i>
                                                <?php endif; ?>
                                            <?php endfor; ?>
                                        </div>
                                        <span><?php echo $rating; ?>/10</span>
                                    </div>
                                </div>
                            <?php else: ?>
                                <div class="no-image-placeholder">No Image Available</div>
                            <?php endif; ?>
                            <div class="movie-selection">
                                <div class="form-check">
                                    <input class="form-check-input movie-checkbox" type="checkbox" value="<?php echo $movie['title']; ?>" id="movie-<?php echo md5($movie['title']); ?>">
                                    <label class="form-check-label" for="movie-<?php echo md5($movie['title']); ?>">
                                        <span class="visually-hidden">Select</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <h5 class="card-title"><?php echo $movie['title']; ?></h5>
                            <div class="movie-year-genre mb-2">
                                <?php
                                    $year = '';
                                    if (preg_match('/Released: (\d{4})/', $movie['content'], $matches)) {
                                        $year = $matches[1];
                                    }
                                ?>
                                <?php if (!empty($year)): ?>
                                    <span class="movie-year"><?php echo $year; ?></span>
                                <?php endif; ?>
                            </div>
                            <p class="card-text"><?php echo substr($movie['description'], 0, 120) . (strlen($movie['description']) > 120 ? '...' : ''); ?></p>
                        </div>
                        <div class="card-footer bg-transparent d-flex justify-content-between">
                            <a href="<?php echo $movie['link']; ?>" class="btn btn-sm btn-outline-primary" target="_blank">
                                <i class="fas fa-external-link-alt me-1"></i> View Details
                            </a>
                            <button class="btn btn-sm btn-outline-secondary movie-select-btn">
                                <i class="fas fa-plus-circle me-1"></i> Select
                            </button>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>

    <div class="row mt-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-body">
                    <h3>Want to explore more options?</h3>
                    <p>Try a different mood target or search for specific movies.</p>
                    <div class="d-flex gap-2 flex-wrap">
                        <a href="<?php echo APP_URL; ?>" class="btn btn-primary">
                            <i class="fas fa-redo"></i> New Mood Check
                        </a>
                        <div class="dropdown">
                            <button class="btn btn-outline-primary dropdown-toggle" type="button" id="targetMoodDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                Change Target Mood
                            </button>
                            <ul class="dropdown-menu" aria-labelledby="targetMoodDropdown">
                                <?php foreach (EMOTION_CATEGORIES as $emotion => $label): ?>
                                    <?php if ($emotion !== $targetEmotion): ?>
                                        <li>
                                            <a class="dropdown-item" href="?source=<?php echo $sourceEmotion; ?>&target=<?php echo $emotion; ?>">
                                                <?php echo $label; ?>
                                            </a>
                                        </li>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="?source=<?php echo $sourceEmotion; ?>&target=energetic">Energetic</a></li>
                                <li><a class="dropdown-item" href="?source=<?php echo $sourceEmotion; ?>&target=focused">Focused</a></li>
                                <li><a class="dropdown-item" href="?source=<?php echo $sourceEmotion; ?>&target=inspired">Inspired</a></li>
                                <li><a class="dropdown-item" href="?source=<?php echo $sourceEmotion; ?>&target=relaxed">Relaxed</a></li>
                            </ul>
                        </div>
                        <a href="<?php echo APP_URL; ?>/pages/tmdb_example.php" class="btn btn-outline-primary">
                            <i class="fas fa-search"></i> Search Movies
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const movieCheckboxes = document.querySelectorAll('.movie-checkbox');
    const saveSelectedBtn = document.getElementById('save-selected-btn');
    const selectedMoviesInput = document.getElementById('selected-movies-input');
    const saveForm = document.getElementById('save-recommendations-form');
    const selectBtns = document.querySelectorAll('.movie-select-btn');

    // Function to update selected movies
    function updateSelectedMovies() {
        const selectedMovies = [];
        movieCheckboxes.forEach(checkbox => {
            if (checkbox.checked) {
                selectedMovies.push(checkbox.value);
            }
        });

        selectedMoviesInput.value = JSON.stringify(selectedMovies);
        saveSelectedBtn.disabled = selectedMovies.length === 0;

        // Update UI to show selection count
        if (selectedMovies.length > 0) {
            saveSelectedBtn.innerHTML = `<i class="fas fa-save"></i> Save Selected Movies (${selectedMovies.length})`;
        } else {
            saveSelectedBtn.innerHTML = `<i class="fas fa-save"></i> Save Selected Movies`;
        }
    }

    // Add event listeners to checkboxes
    movieCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateSelectedMovies);
    });

    // Add event listeners to select buttons
    selectBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const card = this.closest('.movie-card');
            const checkbox = card.querySelector('.movie-checkbox');
            checkbox.checked = !checkbox.checked;

            // Update button text
            if (checkbox.checked) {
                this.innerHTML = '<i class="fas fa-check-circle"></i> Selected';
                this.classList.remove('btn-outline-success');
                this.classList.add('btn-success');
            } else {
                this.innerHTML = '<i class="fas fa-plus-circle"></i> Select';
                this.classList.remove('btn-success');
                this.classList.add('btn-outline-success');
            }

            updateSelectedMovies();
        });
    });

    // Handle save button click
    saveSelectedBtn.addEventListener('click', function() {
        if (selectedMoviesInput.value) {
            saveForm.submit();
        }
    });
});
</script>

<style>
.movie-card .card-img-top {
    height: 300px;
    object-fit: cover;
}

.no-image-placeholder {
    height: 300px;
    background-color: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
}

.movie-selection {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: rgba(255, 255, 255, 0.8);
    padding: 5px;
    border-radius: 5px;
}

.movie-card .card-footer {
    display: flex;
    justify-content: space-between;
}
</style>

<?php
// Include footer
include '../includes/footer.php';
?>

<style>
/* Movie Header */
.movie-header {
    position: relative;
    padding-bottom: 80px;
}

.movie-header .wave-bottom {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    overflow: hidden;
    line-height: 0;
}

.movie-header .wave-bottom svg {
    position: relative;
    display: block;
    width: calc(100% + 1.3px);
    height: 80px;
}

.position-relative.z-index-1 {
    z-index: 1;
}

/* Emotion Badges in Header */
.movie-header .emotion-badge {
    display: inline-block;
    padding: 0.35rem 0.75rem;
    border-radius: 50px;
    font-weight: 600;
    margin: 0 0.25rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* Movie Cards */
.movie-card {
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    overflow: hidden;
}

.movie-card:hover {
    transform: translateY(-15px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.movie-card .card-img-top {
    height: 400px;
    object-fit: cover;
    transition: all 0.5s ease;
}

.movie-card:hover .card-img-top {
    transform: scale(1.05);
}

.movie-card .card-title {
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    line-height: 1.3;
}

.movie-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, rgba(0,0,0,0) 50%, rgba(0,0,0,0.8) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.movie-card:hover .movie-overlay {
    opacity: 1;
}

.movie-rating {
    position: absolute;
    bottom: 20px;
    left: 20px;
    color: white;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.movie-rating .stars {
    margin-bottom: 5px;
}

.movie-rating .stars i {
    color: #f39c12;
    margin-right: 2px;
}

.movie-year-genre {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.movie-year {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    background-color: var(--neutral-200);
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
}

.movie-selection {
    position: absolute;
    top: 15px;
    right: 15px;
    z-index: 10;
}

.movie-selection .form-check {
    min-height: auto;
    margin: 0;
}

.movie-selection .form-check-input {
    width: 1.5rem;
    height: 1.5rem;
    cursor: pointer;
    border: 2px solid white;
    background-color: rgba(255, 255, 255, 0.3);
}

.movie-selection .form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.no-image-placeholder {
    height: 400px;
    background-color: var(--neutral-200);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--neutral-500);
    font-weight: 500;
}

/* Responsive Adjustments */
@media (max-width: 992px) {
    .movie-card .card-img-top {
        height: 350px;
    }
}

@media (max-width: 768px) {
    .movie-card .card-img-top {
        height: 300px;
    }
}
</style>
