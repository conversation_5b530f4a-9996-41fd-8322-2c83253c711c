<?php
/**
 * Emotion Class
 * 
 * Handles emotion detection and processing.
 */
class Emotion {
    private $db;
    
    /**
     * Constructor - Initialize database connection
     */
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * Get all emotions
     * 
     * @return array List of all emotions
     */
    public function getAllEmotions() {
        // Prepare query
        $this->db->query('SELECT * FROM emotions ORDER BY name');
        
        // Get results
        return $this->db->resultSet();
    }
    
    /**
     * Get emotion by ID
     * 
     * @param int $id Emotion ID
     * @return mixed Emotion data if found, false otherwise
     */
    public function getEmotionById($id) {
        // Prepare query
        $this->db->query('SELECT * FROM emotions WHERE id = :id');
        
        // Bind value
        $this->db->bind(':id', $id);
        
        // Get single record
        return $this->db->single();
    }
    
    /**
     * Get emotion by name
     * 
     * @param string $name Emotion name
     * @return mixed Emotion data if found, false otherwise
     */
    public function getEmotionByName($name) {
        // Prepare query
        $this->db->query('SELECT * FROM emotions WHERE name = :name');
        
        // Bind value
        $this->db->bind(':name', $name);
        
        // Get single record
        return $this->db->single();
    }
    
    /**
     * Record user emotion
     * 
     * @param int $userId User ID
     * @param int $emotionId Emotion ID
     * @param float $intensity Emotion intensity (0-1)
     * @return boolean True if successful, false otherwise
     */
    public function recordUserEmotion($userId, $emotionId, $intensity = 1.0) {
        // Prepare query
        $this->db->query('INSERT INTO user_emotions (user_id, emotion_id, intensity) 
                         VALUES (:user_id, :emotion_id, :intensity)');
        
        // Bind values
        $this->db->bind(':user_id', $userId);
        $this->db->bind(':emotion_id', $emotionId);
        $this->db->bind(':intensity', $intensity);
        
        // Execute
        if($this->db->execute()) {
            return $this->db->lastInsertId();
        } else {
            return false;
        }
    }
    
    /**
     * Get user's most recent emotion
     * 
     * @param int $userId User ID
     * @return mixed Emotion data if found, false otherwise
     */
    public function getUserLatestEmotion($userId) {
        // Prepare query
        $this->db->query('SELECT ue.*, e.name as emotion_name 
                         FROM user_emotions ue 
                         JOIN emotions e ON ue.emotion_id = e.id 
                         WHERE ue.user_id = :user_id 
                         ORDER BY ue.timestamp DESC 
                         LIMIT 1');
        
        // Bind value
        $this->db->bind(':user_id', $userId);
        
        // Get single record
        return $this->db->single();
    }
    
    /**
     * Process facial emotion data
     * 
     * @param array $emotionData Emotion data from facial recognition
     * @return string Dominant emotion name
     */
    public function processFacialEmotion($emotionData) {
        // Find the emotion with the highest score
        $maxScore = 0;
        $dominantEmotion = 'neutral';
        
        foreach ($emotionData as $emotion => $score) {
            if ($score > $maxScore) {
                $maxScore = $score;
                $dominantEmotion = $emotion;
            }
        }
        
        return $dominantEmotion;
    }
    
    /**
     * Get opposite or complementary emotion
     * 
     * @param string $emotionName Current emotion name
     * @return string Complementary emotion name
     */
    public function getComplementaryEmotion($emotionName) {
        $emotionPairs = [
            'happy' => 'sad',
            'sad' => 'happy',
            'angry' => 'calm',
            'fearful' => 'confident',
            'surprised' => 'neutral',
            'disgusted' => 'pleased',
            'neutral' => 'happy'
        ];
        
        return isset($emotionPairs[$emotionName]) ? $emotionPairs[$emotionName] : 'happy';
    }
}
?>
