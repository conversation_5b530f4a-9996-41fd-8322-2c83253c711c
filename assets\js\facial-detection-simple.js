/**
 * MoodifyMe - Simple Facial Detection
 * Based on working implementation from Movie Recommendations project
 */

// Global variables
let video;
let currentEmotion = '';

// Initialize facial detection - exactly like working project
async function initFacialDetection() {
    try {
        video = document.getElementById('face-video');
        const emotionResult = document.getElementById('face-result');

        if (!video) {
            console.error('Video element not found');
            return;
        }

        console.log('Initializing facial detection...');

        // Show loading message
        if (emotionResult) {
            emotionResult.innerHTML = `
                <div class="alert alert-info">
                    <p><i class="fas fa-spinner fa-spin"></i> Loading facial detection models...</p>
                    <p>This may take a moment on first load.</p>
                </div>
            `;
        }

        // Load face-api models exactly like the working project
        await Promise.all([
            faceapi.nets.tinyFaceDetector.loadFromUri('assets/models'),
            faceapi.nets.faceLandmark68Net.loadFromUri('assets/models'),
            faceapi.nets.faceRecognitionNet.loadFromUri('assets/models'),
            faceapi.nets.faceExpressionNet.loadFromUri('assets/models')
        ]);

        console.log("Models loaded successfully");

        // Start video exactly like the working project
        const stream = await navigator.mediaDevices.getUserMedia({ video: {} });
        video.srcObject = stream;
        video.play();

        // Show success message
        if (emotionResult) {
            emotionResult.innerHTML = `
                <div class="alert alert-success">
                    <p><i class="fas fa-check-circle"></i> Facial detection ready!</p>
                    <p>Position your face in the camera view.</p>
                </div>
            `;
        }

        // Start detection when video is ready - exactly like working project
        video.addEventListener('loadedmetadata', () => {
            const canvas = faceapi.createCanvasFromMedia(video);
            
            // Position canvas correctly
            const videoContainer = video.parentElement;
            videoContainer.style.position = 'relative';
            videoContainer.appendChild(canvas);

            const displaySize = { width: video.videoWidth, height: video.videoHeight };
            faceapi.matchDimensions(canvas, displaySize);

            canvas.width = displaySize.width;
            canvas.height = displaySize.height;

            // Position canvas over video
            canvas.style.position = 'absolute';
            canvas.style.top = '0';
            canvas.style.left = '0';
            canvas.style.pointerEvents = 'none';

            // Detection loop - exactly like working project
            setInterval(async () => {
                const detections = await faceapi.detectAllFaces(video, new faceapi.TinyFaceDetectorOptions())
                    .withFaceLandmarks()
                    .withFaceExpressions();

                const resizedDetections = faceapi.resizeResults(detections, displaySize);
                const ctx = canvas.getContext('2d');
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                if (detections.length > 0) {
                    console.log("Face detected");
                    
                    // Draw landmarks exactly like working project
                    faceapi.draw.drawFaceLandmarks(canvas, resizedDetections);
                    faceapi.draw.drawFaceExpressions(canvas, resizedDetections);

                    currentEmotion = getDominantEmotion(detections[0].expressions);
                    window.currentEmotion = currentEmotion; // Make it globally accessible
                    console.log("Current emotion:", currentEmotion);
                } else {
                    console.log("No face detected");
                }
            }, 100);
        });

        console.log('Facial detection initialized successfully');

    } catch (error) {
        console.error('Error initializing facial detection:', error);
        const emotionResult = document.getElementById('face-result');
        if (emotionResult) {
            emotionResult.innerHTML = `
                <div class="alert alert-danger">
                    <p><i class="fas fa-exclamation-triangle"></i> Failed to initialize facial detection.</p>
                    <p>Error: ${error.message}</p>
                    <p>Please allow camera access and try again.</p>
                </div>
            `;
        }
    }
}

// Get dominant emotion - exactly like working project
function getDominantEmotion(expressions) {
    return Object.keys(expressions).reduce((a, b) => expressions[a] > expressions[b] ? a : b);
}

// Get random emotion for fallback
function getRandomEmotion() {
    const emotions = ['happy', 'sad', 'angry', 'surprised', 'disgusted', 'fearful'];
    return emotions[Math.floor(Math.random() * emotions.length)];
}

// Make functions globally available
window.initFacialDetection = initFacialDetection;
window.getDominantEmotion = getDominantEmotion;
window.getRandomEmotion = getRandomEmotion;
