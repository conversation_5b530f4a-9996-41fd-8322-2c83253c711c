/**
 * MoodifyMe - Working Facial Detection with CDN
 * Uses official face-api.js CDN and models for reliable facial landmark detection
 */

// Global variables
let video;
let currentEmotion = '';
let isDetecting = false;
let modelsLoaded = false;

// Initialize facial detection with CDN resources
async function initFacialDetection() {
    try {
        console.log('🚀 Starting facial detection initialization...');

        video = document.getElementById('face-video');
        const emotionResult = document.getElementById('face-result');

        console.log('📺 Video element:', video);
        console.log('📊 Result element:', emotionResult);

        if (!video) {
            console.error('❌ Video element with ID "face-video" not found');
            return;
        }

        console.log('✅ Initializing facial detection with CDN...');

        // Check if face-api.js is loaded
        if (typeof faceapi === 'undefined') {
            throw new Error('face-api.js library is not loaded. Please check your internet connection.');
        }
        console.log('✅ face-api.js library is available');

        // Show loading message
        if (emotionResult) {
            emotionResult.innerHTML = `
                <div class="alert alert-info">
                    <p><i class="fas fa-spinner fa-spin"></i> Loading facial detection models from CDN...</p>
                    <p>This may take a moment on first load.</p>
                </div>
            `;
        }

        // Load models from official CDN - this is the key!
        console.log('📦 Loading models from CDN...');
        await Promise.all([
            faceapi.nets.tinyFaceDetector.loadFromUri('https://cdn.jsdelivr.net/npm/face-api.js@0.22.2/weights'),
            faceapi.nets.faceLandmark68Net.loadFromUri('https://cdn.jsdelivr.net/npm/face-api.js@0.22.2/weights'),
            faceapi.nets.faceRecognitionNet.loadFromUri('https://cdn.jsdelivr.net/npm/face-api.js@0.22.2/weights'),
            faceapi.nets.faceExpressionNet.loadFromUri('https://cdn.jsdelivr.net/npm/face-api.js@0.22.2/weights')
        ]);

        modelsLoaded = true;
        console.log("CDN Models loaded successfully!");

        // Start video with detailed logging
        console.log('📹 Starting camera...');

        // Check if getUserMedia is available
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
            throw new Error('getUserMedia is not supported in this browser');
        }

        console.log('📱 Requesting camera access...');
        const constraints = {
            video: {
                width: { ideal: 640 },
                height: { ideal: 480 },
                facingMode: 'user'
            }
        };

        console.log('🎥 Camera constraints:', constraints);
        const stream = await navigator.mediaDevices.getUserMedia(constraints);
        console.log('✅ Camera stream obtained:', stream);

        video.srcObject = stream;
        console.log('📺 Stream assigned to video element');

        await video.play();
        console.log('▶️ Video playing started');

        // Show success message
        if (emotionResult) {
            emotionResult.innerHTML = `
                <div class="alert alert-success">
                    <p><i class="fas fa-check-circle"></i> Facial detection ready!</p>
                    <p>Position your face in the camera view to see landmarks.</p>
                </div>
            `;
        }

        // Start detection when video is ready
        video.addEventListener('loadedmetadata', () => {
            console.log('Video metadata loaded, starting detection...');

            // Create canvas overlay for landmarks
            const canvas = faceapi.createCanvasFromMedia(video);

            // Position canvas correctly
            const videoContainer = video.parentElement;
            videoContainer.style.position = 'relative';
            videoContainer.appendChild(canvas);

            const displaySize = { width: video.videoWidth, height: video.videoHeight };
            faceapi.matchDimensions(canvas, displaySize);

            canvas.width = displaySize.width;
            canvas.height = displaySize.height;

            // Position canvas over video
            canvas.style.position = 'absolute';
            canvas.style.top = '0';
            canvas.style.left = '0';
            canvas.style.pointerEvents = 'none';
            canvas.style.zIndex = '10';

            // Start detection loop - exactly like working project
            isDetecting = true;
            detectFaces(canvas, displaySize);
        });

        console.log('Facial detection initialized successfully with CDN!');

    } catch (error) {
        console.error('❌ Error initializing facial detection:', error);
        console.error('❌ Error name:', error.name);
        console.error('❌ Error message:', error.message);

        const emotionResult = document.getElementById('face-result');
        if (emotionResult) {
            let errorMessage = '';

            if (error.name === 'NotAllowedError') {
                errorMessage = 'Camera access denied. Please allow camera access and try again.';
            } else if (error.name === 'NotFoundError') {
                errorMessage = 'No camera found. Please connect a camera and try again.';
            } else if (error.name === 'NotReadableError') {
                errorMessage = 'Camera is already in use by another application.';
            } else if (error.message.includes('getUserMedia')) {
                errorMessage = 'Camera access is not supported in this browser.';
            } else {
                errorMessage = `${error.message}. Please check your internet connection and try again.`;
            }

            emotionResult.innerHTML = `
                <div class="alert alert-danger">
                    <p><i class="fas fa-exclamation-triangle"></i> Failed to initialize facial detection.</p>
                    <p><strong>Error:</strong> ${errorMessage}</p>
                    <p><small>Error details: ${error.name} - ${error.message}</small></p>
                </div>
            `;
        }
    }
}

// Detection loop function
async function detectFaces(canvas, displaySize) {
    if (!isDetecting || !modelsLoaded || !video) {
        requestAnimationFrame(() => detectFaces(canvas, displaySize));
        return;
    }

    try {
        // Detect faces with landmarks and expressions
        const detections = await faceapi.detectAllFaces(video, new faceapi.TinyFaceDetectorOptions())
            .withFaceLandmarks()
            .withFaceExpressions();

        const resizedDetections = faceapi.resizeResults(detections, displaySize);
        const ctx = canvas.getContext('2d');
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        if (detections.length > 0) {
            console.log("Face detected with CDN models");

            // Draw landmarks exactly like working project - this creates the magenta lines!
            faceapi.draw.drawFaceLandmarks(canvas, resizedDetections);
            faceapi.draw.drawFaceExpressions(canvas, resizedDetections);

            // Get dominant emotion
            currentEmotion = getDominantEmotion(detections[0].expressions);
            window.currentEmotion = currentEmotion; // Make it globally accessible

            const confidence = Math.max(...Object.values(detections[0].expressions));
            console.log("CDN Current emotion:", currentEmotion, "Confidence:", Math.round(confidence * 100) + "%");

            // Update emotion display if available
            const emotionDisplay = document.getElementById('emotion-display');
            if (emotionDisplay) {
                emotionDisplay.innerHTML = `
                    <div class="emotion-info">
                        <strong>Detected: ${currentEmotion}</strong><br>
                        <small>Confidence: ${Math.round(confidence * 100)}%</small>
                    </div>
                `;
            }
        } else {
            console.log("No face detected");
            // Clear emotion if no face detected
            const emotionDisplay = document.getElementById('emotion-display');
            if (emotionDisplay) {
                emotionDisplay.innerHTML = '<small class="text-muted">No face detected</small>';
            }
        }
    } catch (error) {
        console.error('Error in face detection loop:', error);
    }

    // Continue detection loop
    requestAnimationFrame(() => detectFaces(canvas, displaySize));
}

// Get dominant emotion - exactly like working project
function getDominantEmotion(expressions) {
    return Object.keys(expressions).reduce((a, b) => expressions[a] > expressions[b] ? a : b);
}

// Get random emotion for fallback
function getRandomEmotion() {
    const emotions = ['happy', 'sad', 'angry', 'surprised', 'disgusted', 'fearful'];
    return emotions[Math.floor(Math.random() * emotions.length)];
}

// Stop detection (cleanup function)
function stopFacialDetection() {
    isDetecting = false;
    if (video && video.srcObject) {
        const stream = video.srcObject;
        const tracks = stream.getTracks();
        tracks.forEach(track => track.stop());
        video.srcObject = null;
    }
}

// Make functions globally available
window.initFacialDetection = initFacialDetection;
window.stopFacialDetection = stopFacialDetection;
window.getDominantEmotion = getDominantEmotion;
window.getRandomEmotion = getRandomEmotion;

console.log('CDN Facial detection script loaded successfully!');
