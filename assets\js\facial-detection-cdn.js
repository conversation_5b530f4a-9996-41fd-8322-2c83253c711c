/**
 * MoodifyMe - Working Facial Detection with CDN
 * Simplified version based on successful minimal test
 */

// Global variables
let video;
let currentEmotion = '';
let currentStream = null;
let modelsLoaded = false;

// Initialize facial detection - simplified approach
async function initFacialDetection() {
    console.log('🚀 Starting facial detection (simplified)...');

    try {
        video = document.getElementById('face-video');
        const emotionResult = document.getElementById('face-result');

        if (!video) {
            console.error('❌ Video element not found');
            return;
        }

        // Show starting message
        if (emotionResult) {
            emotionResult.innerHTML = `
                <div class="alert alert-info">
                    <p><i class="fas fa-spinner fa-spin"></i> Starting camera...</p>
                </div>
            `;
        }

        // Start camera first (we know this works from minimal test)
        console.log('📹 Starting camera...');

        const constraints = {
            video: {
                width: { ideal: 640 },
                height: { ideal: 480 }
            }
        };

        currentStream = await navigator.mediaDevices.getUserMedia(constraints);
        console.log('✅ Camera stream obtained');

        video.srcObject = currentStream;
        console.log('📺 Stream assigned to video');

        // Wait for video to start
        video.onloadedmetadata = () => {
            console.log('📊 Video metadata loaded');

            if (emotionResult) {
                emotionResult.innerHTML = `
                    <div class="alert alert-success">
                        <p><i class="fas fa-check-circle"></i> Camera started successfully!</p>
                        <p>Now loading facial detection models...</p>
                    </div>
                `;
            }

            // Load face-api models after camera is working
            loadModelsAndStartDetection(emotionResult);
        };

        video.onerror = (error) => {
            console.error('❌ Video error:', error);
            if (emotionResult) {
                emotionResult.innerHTML = `
                    <div class="alert alert-danger">
                        <p><i class="fas fa-exclamation-triangle"></i> Video error occurred</p>
                    </div>
                `;
            }
        };

    } catch (error) {
        console.error('❌ Camera error:', error);

        const emotionResult = document.getElementById('face-result');
        if (emotionResult) {
            let errorMessage = '';

            if (error.name === 'NotAllowedError') {
                errorMessage = 'Camera access denied. Please allow camera access and try again.';
            } else if (error.name === 'NotFoundError') {
                errorMessage = 'No camera found. Please connect a camera and try again.';
            } else if (error.name === 'NotReadableError') {
                errorMessage = 'Camera is already in use by another application.';
            } else {
                errorMessage = error.message;
            }

            emotionResult.innerHTML = `
                <div class="alert alert-danger">
                    <p><i class="fas fa-exclamation-triangle"></i> Camera error</p>
                    <p>${errorMessage}</p>
                </div>
            `;
        }
    }
}

// Load models and start detection after camera is working
async function loadModelsAndStartDetection(emotionResult) {
    try {
        console.log('📦 Loading face-api models...');

        // Check if face-api.js is loaded
        if (typeof faceapi === 'undefined') {
            throw new Error('face-api.js library not loaded');
        }

        if (emotionResult) {
            emotionResult.innerHTML = `
                <div class="alert alert-info">
                    <p><i class="fas fa-spinner fa-spin"></i> Loading facial detection models...</p>
                    <p>This may take a moment...</p>
                </div>
            `;
        }

        // Load models from CDN
        await Promise.all([
            faceapi.nets.tinyFaceDetector.loadFromUri('https://cdn.jsdelivr.net/npm/face-api.js@0.22.2/weights'),
            faceapi.nets.faceLandmark68Net.loadFromUri('https://cdn.jsdelivr.net/npm/face-api.js@0.22.2/weights'),
            faceapi.nets.faceExpressionNet.loadFromUri('https://cdn.jsdelivr.net/npm/face-api.js@0.22.2/weights')
        ]);

        modelsLoaded = true;
        console.log('✅ Models loaded successfully!');

        if (emotionResult) {
            emotionResult.innerHTML = `
                <div class="alert alert-success">
                    <p><i class="fas fa-check-circle"></i> Facial detection ready!</p>
                    <p>Position your face in the camera to see landmarks.</p>
                </div>
            `;
        }

        // Start detection
        startDetection();

    } catch (error) {
        console.error('❌ Model loading error:', error);

        if (emotionResult) {
            emotionResult.innerHTML = `
                <div class="alert alert-warning">
                    <p><i class="fas fa-exclamation-triangle"></i> Could not load facial detection models</p>
                    <p>Camera works, but facial landmarks unavailable. Check internet connection.</p>
                </div>
            `;
        }
    }
}

// Start facial detection
function startDetection() {
    console.log('🎯 Starting facial detection...');

    // Create canvas overlay
    const canvas = faceapi.createCanvasFromMedia(video);
    const videoContainer = video.parentElement;
    videoContainer.style.position = 'relative';
    videoContainer.appendChild(canvas);

    const displaySize = { width: video.videoWidth, height: video.videoHeight };
    faceapi.matchDimensions(canvas, displaySize);

    // Position canvas over video
    canvas.style.position = 'absolute';
    canvas.style.top = '0';
    canvas.style.left = '0';
    canvas.style.pointerEvents = 'none';
    canvas.style.zIndex = '10';

    // Detection loop
    setInterval(async () => {
        if (!modelsLoaded || !video) return;

        try {
            const detections = await faceapi.detectAllFaces(video, new faceapi.TinyFaceDetectorOptions())
                .withFaceLandmarks()
                .withFaceExpressions();

            const resizedDetections = faceapi.resizeResults(detections, displaySize);
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            if (detections.length > 0) {
                console.log("✅ Face detected");

                // Draw landmarks - this creates the magenta lines!
                faceapi.draw.drawFaceLandmarks(canvas, resizedDetections);
                faceapi.draw.drawFaceExpressions(canvas, resizedDetections);

                // Get emotion
                currentEmotion = getDominantEmotion(detections[0].expressions);
                window.currentEmotion = currentEmotion;

                const confidence = Math.max(...Object.values(detections[0].expressions));
                console.log("Current emotion:", currentEmotion, Math.round(confidence * 100) + "%");

                // Update emotion display
                const emotionDisplay = document.getElementById('emotion-display');
                if (emotionDisplay) {
                    emotionDisplay.innerHTML = `
                        <div class="emotion-info">
                            <strong>Detected: ${currentEmotion}</strong><br>
                            <small>Confidence: ${Math.round(confidence * 100)}%</small>
                        </div>
                    `;
                }
            }
        } catch (error) {
            console.error('Detection error:', error);
        }
    }, 100);
}

// Helper function to get dominant emotion
function getDominantEmotion(expressions) {
    let maxEmotion = 'neutral';
    let maxValue = 0;

    for (const [emotion, value] of Object.entries(expressions)) {
        if (value > maxValue) {
            maxValue = value;
            maxEmotion = emotion;
        }
    }

    return maxEmotion;
}

// Make functions globally available
window.initFacialDetection = initFacialDetection;
window.currentEmotion = currentEmotion;

console.log('✅ Simplified facial detection script loaded!');

// Get random emotion for fallback
function getRandomEmotion() {
    const emotions = ['happy', 'sad', 'angry', 'surprised', 'disgusted', 'fearful'];
    return emotions[Math.floor(Math.random() * emotions.length)];
}

// Stop detection (cleanup function)
function stopFacialDetection() {
    isDetecting = false;
    if (video && video.srcObject) {
        const stream = video.srcObject;
        const tracks = stream.getTracks();
        tracks.forEach(track => track.stop());
        video.srcObject = null;
    }
}

// Make functions globally available
window.initFacialDetection = initFacialDetection;
window.stopFacialDetection = stopFacialDetection;
window.getDominantEmotion = getDominantEmotion;
window.getRandomEmotion = getRandomEmotion;

console.log('CDN Facial detection script loaded successfully!');
