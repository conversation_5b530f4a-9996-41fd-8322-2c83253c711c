/**
 * MoodifyMe - Independent Facial Detection System
 * Replicates the exact functionality from Movie-Recommendations project
 * Built specifically for MoodifyMe with no dependencies on external projects
 */

// Global variables for MoodifyMe facial detection
let video;
let captureBtn;
let currentEmotion = '';
let modelsLoaded = false;

// Initialize MoodifyMe facial detection system
function initFacialDetection() {
    console.log('🚀 Initializing MoodifyMe facial detection...');
    
    // Get DOM elements specific to MoodifyMe
    video = document.getElementById('face-video');
    captureBtn = document.getElementById('capture-face');
    
    if (!video) {
        console.error('❌ Video element not found in MoodifyMe');
        showMoodifyMeError('Video element not found. Please refresh the page.');
        return;
    }
    
    // Load face-api models from MoodifyMe assets folder
    Promise.all([
        faceapi.nets.tinyFaceDetector.loadFromUri('assets/models'),
        faceapi.nets.faceLandmark68Net.loadFromUri('assets/models'),
        faceapi.nets.faceRecognitionNet.loadFromUri('assets/models'),
        faceapi.nets.faceExpressionNet.loadFromUri('assets/models')
    ]).then(() => {
        console.log("✅ MoodifyMe facial detection models loaded successfully");
        modelsLoaded = true;
        startMoodifyMeVideo();
        setupMoodifyMeCaptureButton();
    }).catch(err => {
        console.error("❌ MoodifyMe model loading failed:", err);
        showMoodifyMeError('Failed to load facial detection models. Please check your internet connection.');
    });
}

// Start video for MoodifyMe
function startMoodifyMeVideo() {
    console.log('📹 Starting MoodifyMe camera...');
    
    navigator.mediaDevices.getUserMedia({ video: {} })
        .then(stream => {
            video.srcObject = stream;
            video.play();
            console.log('✅ MoodifyMe camera started successfully');
            
            // Show success message in MoodifyMe interface
            const emotionResult = document.getElementById('face-result');
            if (emotionResult) {
                emotionResult.innerHTML = `
                    <div class="alert alert-success">
                        <p><i class="fas fa-check-circle"></i> Camera started successfully!</p>
                        <p>Position your face in the camera view to see facial landmarks.</p>
                    </div>
                `;
            }
            
            // Initialize MoodifyMe video detection
            initMoodifyMeVideoDetection();
        })
        .catch(err => {
            console.error('❌ Error accessing MoodifyMe webcam:', err);
            showMoodifyMeError('Error accessing camera. Please allow camera access and try again.');
        });
}

// Initialize video detection for MoodifyMe - exactly like Movie-Recommendations
function initMoodifyMeVideoDetection() {
    if (!video) return;
    
    video.addEventListener('loadedmetadata', () => {
        console.log('📊 MoodifyMe video metadata loaded, starting detection...');
        
        // Create canvas overlay exactly like Movie-Recommendations
        const canvas = faceapi.createCanvasFromMedia(video);
        const videoContainer = video.parentElement;
        videoContainer.style.position = 'relative';
        videoContainer.appendChild(canvas);

        const displaySize = { width: video.videoWidth, height: video.videoHeight };
        faceapi.matchDimensions(canvas, displaySize);

        canvas.width = displaySize.width;
        canvas.height = displaySize.height;
        
        // Position canvas over video - exactly like Movie-Recommendations
        canvas.style.position = 'absolute';
        canvas.style.top = '0';
        canvas.style.left = '0';
        canvas.style.pointerEvents = 'none';
        canvas.style.zIndex = '10';

        // Start detection loop - exactly like Movie-Recommendations
        setInterval(async () => {
            if (!modelsLoaded) return;
            
            const detections = await faceapi.detectAllFaces(video, new faceapi.TinyFaceDetectorOptions())
                .withFaceLandmarks()
                .withFaceExpressions();

            const resizedDetections = faceapi.resizeResults(detections, displaySize);
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // Translate the canvas - exactly like Movie-Recommendations
            ctx.save();
            ctx.translate(0, 0);

            if (detections.length > 0) {
                console.log("✅ Face detected in MoodifyMe");
                
                // Draw facial landmarks - this creates the magenta/pink lines!
                faceapi.draw.drawFaceLandmarks(canvas, resizedDetections);
                faceapi.draw.drawFaceExpressions(canvas, resizedDetections);

                currentEmotion = getMoodifyMeDominantEmotion(detections[0].expressions);
                console.log("MoodifyMe current emotion:", currentEmotion);
                
                // Update MoodifyMe emotion display
                updateMoodifyMeEmotionDisplay(currentEmotion, detections[0].expressions);
            } else {
                console.log("No face detected in MoodifyMe");
                updateMoodifyMeEmotionDisplay(null);
            }
            ctx.restore();
        }, 100);
    });
}

// Setup capture button for MoodifyMe
function setupMoodifyMeCaptureButton() {
    const captureBtn = document.getElementById('capture-face');
    if (captureBtn) {
        captureBtn.addEventListener('click', () => {
            console.log('🎯 MoodifyMe capture button clicked');
            
            const emotionToShow = currentEmotion === 'neutral' ? getMoodifyMeRandomEmotion() : currentEmotion || 'neutral';
            console.log('MoodifyMe using emotion:', emotionToShow);
            
            // Call MoodifyMe's emotion results function
            if (typeof window.showEmotionResults === 'function') {
                window.showEmotionResults(emotionToShow, 0.8);
            } else {
                // Fallback - redirect to MoodifyMe mood options
                const basePath = window.location.pathname.includes('/pages/') ? '' : 'pages/';
                window.location.href = `${basePath}mood_options.php?source=${emotionToShow}&target=happy`;
            }
        });
    }
}

// Helper functions for MoodifyMe
function getMoodifyMeDominantEmotion(expressions) {
    return Object.keys(expressions).reduce((a, b) => expressions[a] > expressions[b] ? a : b);
}

function getMoodifyMeRandomEmotion() {
    const emotions = ['happy', 'sad', 'angry', 'surprised', 'disgusted', 'fearful'];
    return emotions[Math.floor(Math.random() * emotions.length)];
}

function updateMoodifyMeEmotionDisplay(emotion, expressions) {
    const emotionDisplay = document.getElementById('emotion-display');
    if (emotionDisplay) {
        if (emotion) {
            const confidence = expressions ? Math.max(...Object.values(expressions)) : 0.8;
            emotionDisplay.innerHTML = `
                <div class="emotion-info">
                    <strong>Detected: ${emotion}</strong><br>
                    <small>Confidence: ${Math.round(confidence * 100)}%</small>
                </div>
            `;
        } else {
            emotionDisplay.innerHTML = '<small class="text-muted">No face detected</small>';
        }
    }
}

function showMoodifyMeError(message) {
    const emotionResult = document.getElementById('face-result');
    if (emotionResult) {
        emotionResult.innerHTML = `
            <div class="alert alert-danger">
                <p><i class="fas fa-exclamation-triangle"></i> ${message}</p>
            </div>
        `;
    }
}

// Make MoodifyMe facial detection globally available
window.initFacialDetection = initFacialDetection;
window.currentEmotion = currentEmotion;

console.log('✅ MoodifyMe independent facial detection system loaded!');
