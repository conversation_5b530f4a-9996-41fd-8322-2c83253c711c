<?php
/**
 * Application Configuration
 * 
 * This file contains general configuration settings for the application.
 */

// Application settings
define('APP_NAME', 'Emotion-Based Recommendation System');
define('APP_VERSION', '1.0.0');
define('BASE_URL', 'http://localhost/Movie-Recommendations-based-on-facial-expressions-using-Machine-Learning-main');

// API Keys
define('TMDB_API_KEY', 'YOUR_TMDB_API_KEY'); // Replace with your actual TMDB API key
define('SPOTIFY_API_KEY', 'YOUR_SPOTIFY_API_KEY'); // Replace with your actual Spotify API key (if using)

// File paths
define('ROOT_PATH', dirname(__DIR__));
define('INCLUDE_PATH', ROOT_PATH . '/includes');
define('CLASS_PATH', ROOT_PATH . '/classes');
define('API_PATH', ROOT_PATH . '/api');
define('ASSET_PATH', ROOT_PATH . '/assets');

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Session configuration
ini_set('session.cookie_httponly', 1);
ini_set('session.use_only_cookies', 1);
ini_set('session.cookie_secure', 0); // Set to 1 if using HTTPS

// Time zone
date_default_timezone_set('UTC');

// Helper functions
function redirect($url) {
    header('Location: ' . $url);
    exit;
}

function sanitize($data) {
    return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
}

function generateRandomString($length = 10) {
    return bin2hex(random_bytes($length));
}

function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

function requireLogin() {
    if (!isLoggedIn()) {
        redirect(BASE_URL . '/pages/login.php');
    }
}

function getEmotionName($emotionId) {
    global $conn;
    $stmt = $conn->prepare("SELECT name FROM emotions WHERE id = ?");
    $stmt->bind_param("i", $emotionId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($row = $result->fetch_assoc()) {
        return $row['name'];
    }
    
    return 'unknown';
}
?>
