<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Facial Detection Diagnostic</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #1a1a1a; color: white; }
        .log { background: #333; padding: 10px; margin: 10px 0; border-radius: 5px; font-family: monospace; }
        .error { background: #d32f2f; }
        .success { background: #388e3c; }
        .warning { background: #f57c00; }
        button { background: #E55100; color: white; border: none; padding: 10px 20px; border-radius: 5px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>🔧 Facial Detection Diagnostic Tool</h1>
    
    <div class="controls">
        <button onclick="checkBrowser()">Check Browser Support</button>
        <button onclick="checkCamera()">Check Camera Access</button>
        <button onclick="checkFaceAPI()">Check Face-API.js</button>
        <button onclick="checkModels()">Check Models</button>
        <button onclick="runFullTest()">Run Full Test</button>
    </div>
    
    <div id="logs"></div>
    
    <script src="face-api.min.js"></script>
    <script>
        function log(message, type = 'info') {
            const logs = document.getElementById('logs');
            const div = document.createElement('div');
            div.className = `log ${type}`;
            div.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            logs.appendChild(div);
            console.log(message);
        }
        
        function checkBrowser() {
            log('🌐 Checking browser support...');
            
            // Check if running on localhost or HTTPS
            const isSecure = location.protocol === 'https:' || location.hostname === 'localhost' || location.hostname === '127.0.0.1';
            log(`Protocol: ${location.protocol}, Hostname: ${location.hostname}`, isSecure ? 'success' : 'warning');
            
            // Check getUserMedia support
            const hasGetUserMedia = !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);
            log(`getUserMedia support: ${hasGetUserMedia}`, hasGetUserMedia ? 'success' : 'error');
            
            // Check WebGL support
            const canvas = document.createElement('canvas');
            const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
            log(`WebGL support: ${!!gl}`, gl ? 'success' : 'error');
            
            // Check if face-api.js is loaded
            log(`face-api.js loaded: ${typeof faceapi !== 'undefined'}`, typeof faceapi !== 'undefined' ? 'success' : 'error');
        }
        
        async function checkCamera() {
            log('📹 Checking camera access...');
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ video: true });
                log('✅ Camera access granted', 'success');
                
                // Stop the stream
                stream.getTracks().forEach(track => track.stop());
                return true;
            } catch (error) {
                log(`❌ Camera access failed: ${error.message}`, 'error');
                return false;
            }
        }
        
        function checkFaceAPI() {
            log('🤖 Checking Face-API.js...');
            
            if (typeof faceapi === 'undefined') {
                log('❌ face-api.js not loaded', 'error');
                return false;
            }
            
            log('✅ face-api.js is loaded', 'success');
            
            // Check available nets
            const nets = ['tinyFaceDetector', 'faceLandmark68Net', 'faceExpressionNet', 'faceRecognitionNet'];
            nets.forEach(net => {
                const available = faceapi.nets[net] !== undefined;
                log(`${net}: ${available ? '✅' : '❌'}`, available ? 'success' : 'error');
            });
            
            return true;
        }
        
        async function checkModels() {
            log('📦 Checking model files...');
            
            const modelFiles = [
                'models/tiny_face_detector_model-weights_manifest.json',
                'models/face_landmark_68_model-weights_manifest.json',
                'models/face_expression_model-weights_manifest.json',
                'models/face_recognition_model-weights_manifest.json'
            ];
            
            for (const file of modelFiles) {
                try {
                    const response = await fetch(file);
                    if (response.ok) {
                        log(`✅ ${file} - OK`, 'success');
                    } else {
                        log(`❌ ${file} - ${response.status}`, 'error');
                    }
                } catch (error) {
                    log(`❌ ${file} - ${error.message}`, 'error');
                }
            }
        }
        
        async function runFullTest() {
            log('🚀 Running full facial detection test...');
            
            try {
                // Load models
                log('Loading models...');
                await Promise.all([
                    faceapi.nets.tinyFaceDetector.loadFromUri('./models'),
                    faceapi.nets.faceLandmark68Net.loadFromUri('./models'),
                    faceapi.nets.faceRecognitionNet.loadFromUri('./models'),
                    faceapi.nets.faceExpressionNet.loadFromUri('./models')
                ]);
                log('✅ All models loaded successfully', 'success');
                
                // Test camera
                const stream = await navigator.mediaDevices.getUserMedia({ video: true });
                log('✅ Camera stream obtained', 'success');
                
                // Create video element
                const video = document.createElement('video');
                video.srcObject = stream;
                video.autoplay = true;
                video.muted = true;
                video.style.width = '320px';
                video.style.height = '240px';
                document.body.appendChild(video);
                
                // Wait for video to load
                await new Promise(resolve => {
                    video.onloadedmetadata = resolve;
                });
                
                log('✅ Video stream ready', 'success');
                
                // Test face detection
                setTimeout(async () => {
                    try {
                        const detections = await faceapi.detectAllFaces(video, new faceapi.TinyFaceDetectorOptions())
                            .withFaceLandmarks()
                            .withFaceExpressions();
                        
                        if (detections.length > 0) {
                            log(`✅ Face detection working! Found ${detections.length} face(s)`, 'success');
                            
                            // Test landmark drawing
                            const canvas = faceapi.createCanvasFromMedia(video);
                            document.body.appendChild(canvas);
                            
                            const displaySize = { width: video.videoWidth, height: video.videoHeight };
                            faceapi.matchDimensions(canvas, displaySize);
                            
                            const resizedDetections = faceapi.resizeResults(detections, displaySize);
                            faceapi.draw.drawFaceLandmarks(canvas, resizedDetections);
                            faceapi.draw.drawFaceExpressions(canvas, resizedDetections);
                            
                            log('✅ Facial landmarks drawn successfully!', 'success');
                            
                            // Get emotion
                            const expressions = detections[0].expressions;
                            const emotion = Object.keys(expressions).reduce((a, b) => expressions[a] > expressions[b] ? a : b);
                            log(`✅ Detected emotion: ${emotion} (${Math.round(expressions[emotion] * 100)}%)`, 'success');
                            
                        } else {
                            log('⚠️ No faces detected. Make sure your face is visible.', 'warning');
                        }
                    } catch (error) {
                        log(`❌ Face detection failed: ${error.message}`, 'error');
                    }
                }, 2000);
                
            } catch (error) {
                log(`❌ Full test failed: ${error.message}`, 'error');
            }
        }
        
        // Auto-run basic checks on load
        window.onload = () => {
            log('🔧 Diagnostic tool loaded');
            checkBrowser();
        };
    </script>
</body>
</html>
