Create a web page similar to a mood detection application with the following features:

1. A webcam feed that captures the user's face
2. Face detection and emotion recognition functionality that:
   - Draws facial landmark lines on the user's face in real-time
   - Detects emotions (happy, sad, angry, surprised, disgusted, fearful, neutral)
   - Calculates and displays confidence percentages for each detected emotion

3. A clean, dark-themed UI with the following elements:
   - A header with instructions for the user
   - A centered video element for the webcam feed
   - A canvas overlay for drawing face landmarks and expression confidence
   - A "Capture Emotion" button below the video
   - Responsive design that works well on different screen sizes

The page should:
- Load face detection models (face-api.js)
- Access the user's webcam
- Display facial landmark lines overlaid on the user's face
- Detect facial expressions in real-time with confidence scores
- Allow users to capture their current dominant emotion
- Redirect to a results page with the detected emotion and recommended content

Please provide the complete HTML, CSS, and JavaScript code needed to implement this functionality, similar to the moodDetector.html implementation with face-api.js.