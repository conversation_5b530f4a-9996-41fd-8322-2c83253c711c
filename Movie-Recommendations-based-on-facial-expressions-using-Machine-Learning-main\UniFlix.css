@font-face {
    font-family: font;
    src: url(fonts/font.otf);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-size: 1.5vw;
    user-select: none;
    font-family: "font";
    cursor: url(icons/cursor.png), auto;
}

body {
    background: black;
    overflow: hidden;
}

.head {
    width: 100%;
    height: 100vh;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

nav {
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    padding: 1.25%;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.icon {
    width: 2.75vw;
    height: 2.75vw;
}

nav .ul1 .li1 {
    list-style: none;
    display: inline-block;
    margin-left: 2.5vw;
    margin-right: 0.75vw;
}

.a1 {
    text-decoration: none;
    position: relative;
    padding-bottom: 0.75vw;
    transition: 0.25s;
    color: white;
}

.a1::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 0.25vw;
    border-radius: 1vw;
    background-image: radial-gradient(circle farthest-corner at 92.3% 71.5%, rgba(83, 138, 214, 1) 0%, rgba(134, 231, 214, 1) 90%);
    bottom: 0;
    left: 0;
    transform-origin: right;
    transform: scaleX(0);
    background-size: 200%;
    animation: flow 2.5s ease-in-out infinite;
    transition: transform 0.25s ease-in-out;
}

.a1:hover::before {
    transform-origin: left;
    transform: scaleX(1);
}

.logsig {
    text-align: center;
}

h1 {
    color: white;
    font-size: 5vw;
    letter-spacing: 1.5vw;
    padding-bottom: 2.5vw;
}

h3 {
    font-size: 2.25vw;
    font-style: italic;
    animation: flow 2.5s ease-in-out infinite;
    background-image: radial-gradient(circle farthest-corner at 92.3% 71.5%, rgba(83, 138, 214, 1) 0%, rgba(134, 231, 214, 1) 90%);
    background-size: 200%;
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

h3 span {
    font-size: 2.25vw;
}

@keyframes flow {
    0% {
        background-position: 0 50%;
    }

    50% {
        background-position: 100% 50%;
    }

    100% {
        background-position: 0 50%;
    }
}

@keyframes rotate {
    from {
        rotate: 0deg;
    }

    to {
        rotate: 360deg;
    }
}

.blob {
    position: absolute;
    top: 50%;
    left: 50%;
    height: 25vw;
    border-radius: 50%;
    translate: -50% -50%;
    aspect-ratio: 1;
    background: linear-gradient(to right, #8360c3, #2ebf91);
    animation: rotate 25s infinite;
    filter: blur(10vw);
}

.span1 {
    font-size: 1.75vw;
}

button {
    border: none;
    background-color: transparent;
    border: none;
    color: white;
    font-size: 5vw;
    font-style: bold;
    width: 11.75vw;
    padding: 0.5%;
    transition: all 0.75s;
}

button .span1 {
    display: inline-block;
    position: relative;
    transition: 0.75s;
}

button .span1:after {
    content: "\00BB";
    position: absolute;
    opacity: 0;
    top: 0;
    right: 2.5vw;
    transition: 0.75s;
}

button:hover .span1 {
    padding-right: 22.5%;
}

button:hover .span1:after {
    opacity: 1;
    right: 0;
}

.smedia {
    position: fixed;
    bottom: 1.5vw;
    right: 1.5vw;
}

.a2 {
    text-decoration: none;
}

.ul2 .li2 {
    width: auto;
    float: left;
    list-style: none;
    width: 3.25vw;
    height: 3.25vw;
    margin-left: 0.75vw;
}

.ul2 .li2 .span2 {
    border: 0.125vw solid white;
    border-radius: 50%;
    width: 100%;
    height: 100%;
    display: flex;
    background-color: transparent;
    justify-content: center;
    align-items: center;
    color: white;
    font-size: 3.25vw;
    transition: 0.75s ease-out;
}

.ul2 .li2:hover .span2 {
    transition: 0.5s;
}

.ul2 .li2:nth-child(1):hover .span2 {
    background: #1DA1F2;
}

.ul2 .li2:nth-child(2):hover .span2 {
    background: #4267B2;
}

.ul2 .li2:nth-child(3):hover .span2 {
    background: #0077b5;
}

.ul2 .li2:nth-child(4):hover .span2 {
    background: #8a3ab9;
}

.ul2 .li2:nth-child(5):hover .span2 {
    background: #FF0000;
}

#preloader {
    background: black url(images/preloader.gif) no-repeat center;
    background-size: 17.5%;
    width: 100%;
    height: 100vh;
    position: fixed;
    z-index: 100;
}