// Emotion Detector JavaScript

// DOM Elements
const video = document.getElementById('video');
const canvas = document.getElementById('canvas');
const startCameraBtn = document.getElementById('start-camera');
const captureEmotionBtn = document.getElementById('capture-emotion');
const detectedEmotionSpan = document.getElementById('detected-emotion');
const facialResultDiv = document.getElementById('facial-result');
const emotionActionsDiv = document.querySelector('.emotion-actions');
const getRecommendationsBtn = document.getElementById('get-recommendations');
const emotionButtons = document.querySelectorAll('.emotion-button');

// Text analysis elements
const emotionText = document.getElementById('emotion-text');
const analyzeTextBtn = document.getElementById('analyze-text');
const textResultDiv = document.getElementById('text-result');
const textDetectedEmotionSpan = document.getElementById('text-detected-emotion');
const textEmotionDescriptionSpan = document.getElementById('text-emotion-description');

// Method tabs
const methodTabs = document.querySelectorAll('.method-tab');
const methodPanels = document.querySelectorAll('.method-panel');

// Variables
let currentEmotion = '';
let targetEmotion = '';
let detectionMethod = 'facial';
let faceDetectionInterval;
let emotionConfidence = {};

// Load face-api models
async function loadModels() {
    try {
        await Promise.all([
            faceapi.nets.tinyFaceDetector.loadFromUri('../assets/models/face-api'),
            faceapi.nets.faceLandmark68Net.loadFromUri('../assets/models/face-api'),
            faceapi.nets.faceRecognitionNet.loadFromUri('../assets/models/face-api'),
            faceapi.nets.faceExpressionNet.loadFromUri('../assets/models/face-api')
        ]);
        console.log('Models loaded successfully');
        startCameraBtn.disabled = false;
    } catch (err) {
        console.error('Error loading models:', err);
        alert('Error loading facial recognition models. Please try again later.');
    }
}

// Start video stream
async function startVideo() {
    try {
        const stream = await navigator.mediaDevices.getUserMedia({ video: {} });
        video.srcObject = stream;
        await video.play();
        
        // Set canvas dimensions
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        
        // Enable capture button
        captureEmotionBtn.disabled = false;
        
        // Start face detection
        startFaceDetection();
    } catch (err) {
        console.error('Error accessing webcam:', err);
        alert('Error accessing webcam. Please make sure your camera is connected and permissions are granted.');
    }
}

// Start face detection
function startFaceDetection() {
    faceDetectionInterval = setInterval(async () => {
        const detections = await faceapi.detectAllFaces(
            video, 
            new faceapi.TinyFaceDetectorOptions()
        ).withFaceLandmarks().withFaceExpressions();
        
        const ctx = canvas.getContext('2d');
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        if (detections.length > 0) {
            // Draw face landmarks and expressions
            const resizedDetections = faceapi.resizeResults(detections, {
                width: canvas.width,
                height: canvas.height
            });
            
            faceapi.draw.drawFaceLandmarks(canvas, resizedDetections);
            
            // Store emotion confidence values
            emotionConfidence = detections[0].expressions;
            currentEmotion = getDominantEmotion(emotionConfidence);
        }
    }, 100);
}

// Get dominant emotion
function getDominantEmotion(expressions) {
    return Object.keys(expressions).reduce((a, b) => 
        expressions[a] > expressions[b] ? a : b
    );
}

// Capture emotion
function captureEmotion() {
    clearInterval(faceDetectionInterval);
    
    // Display detected emotion
    detectedEmotionSpan.textContent = currentEmotion ? capitalizeFirstLetter(currentEmotion) : 'Neutral';
    facialResultDiv.style.display = 'block';
    
    // Update confidence bars
    for (const emotion in emotionConfidence) {
        const confidenceBar = document.getElementById(`confidence-${emotion}`);
        const confidenceValue = document.getElementById(`confidence-value-${emotion}`);
        
        if (confidenceBar && confidenceValue) {
            const percentage = Math.round(emotionConfidence[emotion] * 100);
            confidenceBar.style.width = `${percentage}%`;
            confidenceValue.textContent = `${percentage}%`;
        }
    }
    
    // Show emotion actions
    emotionActionsDiv.style.display = 'block';
}

// Analyze text for emotion
function analyzeText() {
    const text = emotionText.value.trim();
    
    if (text.length === 0) {
        alert('Please enter some text describing your feelings.');
        return;
    }
    
    // In a real application, this would call an NLP API
    // For this example, we'll use a simple keyword-based approach
    const emotionKeywords = {
        happy: ['happy', 'joy', 'excited', 'great', 'wonderful', 'pleased', 'delighted'],
        sad: ['sad', 'unhappy', 'depressed', 'down', 'miserable', 'gloomy', 'heartbroken'],
        angry: ['angry', 'mad', 'furious', 'annoyed', 'irritated', 'frustrated', 'enraged'],
        fearful: ['afraid', 'scared', 'fearful', 'terrified', 'anxious', 'worried', 'nervous'],
        surprised: ['surprised', 'shocked', 'amazed', 'astonished', 'stunned', 'unexpected'],
        disgusted: ['disgusted', 'revolted', 'repulsed', 'sickened', 'appalled', 'horrified'],
        neutral: ['okay', 'fine', 'neutral', 'normal', 'average', 'indifferent']
    };
    
    // Count occurrences of emotion keywords
    const emotionCounts = {};
    const words = text.toLowerCase().split(/\W+/);
    
    for (const emotion in emotionKeywords) {
        emotionCounts[emotion] = 0;
        
        for (const word of words) {
            if (emotionKeywords[emotion].includes(word)) {
                emotionCounts[emotion]++;
            }
        }
    }
    
    // Determine dominant emotion
    currentEmotion = Object.keys(emotionCounts).reduce((a, b) => 
        emotionCounts[a] > emotionCounts[b] ? a : b
    );
    
    // If no emotion keywords found, default to neutral
    if (Object.values(emotionCounts).every(count => count === 0)) {
        currentEmotion = 'neutral';
    }
    
    // Display detected emotion
    textDetectedEmotionSpan.textContent = capitalizeFirstLetter(currentEmotion);
    
    // Set emotion description
    const emotionDescriptions = {
        happy: 'happy and joyful',
        sad: 'sad or down',
        angry: 'angry or frustrated',
        fearful: 'fearful or anxious',
        surprised: 'surprised or shocked',
        disgusted: 'disgusted or repulsed',
        neutral: 'neutral or indifferent'
    };
    
    textEmotionDescriptionSpan.textContent = emotionDescriptions[currentEmotion];
    
    // Show results and emotion actions
    textResultDiv.style.display = 'block';
    emotionActionsDiv.style.display = 'block';
}

// Helper function to capitalize first letter
function capitalizeFirstLetter(string) {
    return string.charAt(0).toUpperCase() + string.slice(1);
}

// Event Listeners
document.addEventListener('DOMContentLoaded', loadModels);

startCameraBtn.addEventListener('click', startVideo);
captureEmotionBtn.addEventListener('click', captureEmotion);
analyzeTextBtn.addEventListener('click', analyzeText);

// Method tab switching
methodTabs.forEach(tab => {
    tab.addEventListener('click', () => {
        // Update active tab
        methodTabs.forEach(t => t.classList.remove('active'));
        tab.classList.add('active');
        
        // Update active panel
        const method = tab.dataset.method;
        detectionMethod = method;
        
        methodPanels.forEach(panel => panel.classList.remove('active'));
        document.getElementById(`${method}-panel`).classList.add('active');
        
        // Reset results
        facialResultDiv.style.display = 'none';
        textResultDiv.style.display = 'none';
        emotionActionsDiv.style.display = 'none';
    });
});

// Emotion target selection
emotionButtons.forEach(button => {
    button.addEventListener('click', () => {
        // Update active button
        emotionButtons.forEach(b => b.classList.remove('active'));
        button.classList.add('active');
        
        // Set target emotion
        targetEmotion = button.dataset.emotion;
        
        // Enable get recommendations button
        getRecommendationsBtn.disabled = false;
    });
});

// Get recommendations
getRecommendationsBtn.addEventListener('click', () => {
    if (!currentEmotion) {
        alert('Please detect your emotion first.');
        return;
    }
    
    if (!targetEmotion) {
        alert('Please select a target emotion.');
        return;
    }
    
    // Redirect to recommendations page
    window.location.href = `recommendations.php?emotion=${currentEmotion}&target=${targetEmotion}&type=movie`;
});
