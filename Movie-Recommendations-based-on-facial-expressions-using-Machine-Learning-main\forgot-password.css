@font-face {
  font-family: font;
  src: url(fonts/font.otf);
}
@keyframes rotate {
  from {
      rotate: 0deg;
  }

  to {
      rotate: 360deg;
  }
}

.form-box{
  display:grid;
  row-gap: 1rem;
}

* {
  margin: 0;
  padding: 0;
  font-family: "font";
  cursor: url(icons/cursor.png), auto;
  user-select: none;
}
h1 {
  color: white;
  font-size: 5vw;
  
  padding-bottom: 2.5vw;
}
.head {
  width: 100%;
  height: 100vh;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}
nav {
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  padding: 1.25%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
nav .ul1 .li1 {
  list-style: none;
  display: inline-block;
  margin-left: 2.5vw;
  margin-right: 0.75vw;
}

.icon {
  width: 2.75vw;
  height: 2.75vw;
}

body {
  background: black;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
}
.form-box {
  position: relative;
  width: 30.5vw;
  height: 31.25vw;
  background: transparent;
  border-radius: 1.95vw;
  backdrop-filter: blur(35px);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0 5px white;
}
.input {
  position: relative;
  margin: 2.73vw 0;
  width: 22.5vw;
  border-bottom: 0.14vw solid white;
}

input:focus~label,
input:valid~label,
input:not(:placeholder-shown):invalid~label {
    top: -0.2vw;
}

.input label {
  position: absolute;
  top: 50%;
  left: 0.2vw;
  color: white;
  transform: translateY(-50%);
  font-size: 1.27vw;
  pointer-events: none;
  transition: 0.25s;
}
.input input {
  width: 87.5%;
  height: 3.52vw;
  background: transparent;
  border: none;
  outline: none;
  font-size: 1.27vw;
  padding: 0 3.91vw 0 0.2vw;
  color: white;
}

section {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  width: 100%;
}

.input{
    width: 87.5%;
    height: 3.52vw;
    background: transparent;
    border: none;
    outline: none;
    font-size: 1.27vw;
    padding: 0 3.91vw 0 0.2vw;
    color: white;
}
.input label {
  position: absolute;
  top: 50%;
  left: 0.2vw;
  color: white;
  transform: translateY(-50%);
  font-size: 1.27vw;
  pointer-events: none;
  transition: 0.25s;
}

button {
  width: 100%;
  height: 2.93vw;
  background: white;
  border-radius: 1.95vw;
  border: none;
  outline: none;
  font-size: 1.37vw;
  font-weight: 600;
  position: absolute;
}