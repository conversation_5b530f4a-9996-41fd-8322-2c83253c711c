<nav class="sidebar">
    <div class="sidebar-header">
        <h3>Categories</h3>
    </div>
    
    <ul class="sidebar-menu">
        <li class="sidebar-item">
            <a href="<?php echo BASE_URL; ?>/pages/recommendations.php?type=movie" class="sidebar-link">
                <i class="fas fa-film"></i>
                <span>Movies</span>
            </a>
        </li>
        <li class="sidebar-item">
            <a href="<?php echo BASE_URL; ?>/pages/recommendations.php?type=music" class="sidebar-link">
                <i class="fas fa-music"></i>
                <span>Music</span>
            </a>
        </li>
        <li class="sidebar-item">
            <a href="<?php echo BASE_URL; ?>/pages/recommendations.php?type=meal" class="sidebar-link">
                <i class="fas fa-utensils"></i>
                <span>Meals</span>
            </a>
        </li>
        <li class="sidebar-item">
            <a href="<?php echo BASE_URL; ?>/pages/recommendations.php?type=joke" class="sidebar-link">
                <i class="fas fa-laugh"></i>
                <span>Jokes</span>
            </a>
        </li>
    </ul>
    
    <div class="sidebar-header">
        <h3>Emotions</h3>
    </div>
    
    <ul class="sidebar-menu">
        <?php
        // Get all emotions from the database
        require_once CLASS_PATH . '/Emotion.php';
        $emotionObj = new Emotion();
        $emotions = $emotionObj->getAllEmotions();
        
        foreach ($emotions as $emotion) {
            $icon = 'fas fa-meh'; // Default icon
            
            // Set appropriate icon based on emotion name
            switch ($emotion['name']) {
                case 'happy':
                    $icon = 'fas fa-smile';
                    break;
                case 'sad':
                    $icon = 'fas fa-sad-tear';
                    break;
                case 'angry':
                    $icon = 'fas fa-angry';
                    break;
                case 'fearful':
                    $icon = 'fas fa-grimace';
                    break;
                case 'surprised':
                    $icon = 'fas fa-surprise';
                    break;
                case 'disgusted':
                    $icon = 'fas fa-dizzy';
                    break;
                case 'neutral':
                    $icon = 'fas fa-meh';
                    break;
            }
            
            echo '<li class="sidebar-item">';
            echo '<a href="' . BASE_URL . '/pages/recommendations.php?emotion=' . $emotion['name'] . '" class="sidebar-link">';
            echo '<i class="' . $icon . '"></i>';
            echo '<span>' . ucfirst($emotion['name']) . '</span>';
            echo '</a>';
            echo '</li>';
        }
        ?>
    </ul>
</nav>
