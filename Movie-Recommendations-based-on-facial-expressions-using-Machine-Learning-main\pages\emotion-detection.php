<?php
// Emotion detection page
session_start();
require_once '../config/config.php';
require_once '../config/database.php';
require_once '../classes/Database.php';
require_once '../classes/User.php';
require_once '../classes/Emotion.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

$userId = $_SESSION['user_id'];
$userObj = new User();
$user = $userObj->getUserById($userId);

$emotionObj = new Emotion();
$emotions = $emotionObj->getAllEmotions();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Detect Your Mood - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/style.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/emotion.css">
    <script src="https://kit.fontawesome.com/7331d8ff14.js"></script>
    <script defer src="<?php echo BASE_URL; ?>/assets/models/face-api/face-api.min.js"></script>
</head>
<body>
    <?php include '../includes/header.php'; ?>
    
    <main>
        <?php include '../includes/sidebar.php'; ?>
        
        <div class="container">
            <div class="emotion-detection-container">
                <h1>Detect Your Mood</h1>
                <p>Let us analyze your facial expression to detect your current mood.</p>
                
                <div class="detection-methods">
                    <div class="method-tabs">
                        <button class="method-tab active" data-method="facial">Facial Expression</button>
                        <button class="method-tab" data-method="text">Text Input</button>
                    </div>
                    
                    <div class="method-content">
                        <!-- Facial Expression Detection -->
                        <div class="method-panel active" id="facial-panel">
                            <div class="video-container">
                                <video id="video" autoplay muted></video>
                                <canvas id="canvas"></canvas>
                                <div class="video-overlay">
                                    <div class="face-guide"></div>
                                    <p>Position your face within the circle</p>
                                </div>
                            </div>
                            
                            <div class="detection-controls">
                                <button id="start-camera" class="btn primary-btn">
                                    <i class="fas fa-camera"></i> Start Camera
                                </button>
                                <button id="capture-emotion" class="btn secondary-btn" disabled>
                                    <i class="fas fa-smile"></i> Capture Emotion
                                </button>
                            </div>
                            
                            <div class="detection-result" id="facial-result" style="display: none;">
                                <h3>Detected Emotion: <span id="detected-emotion">-</span></h3>
                                <div class="emotion-confidence">
                                    <div class="confidence-bars">
                                        <?php foreach ($emotions as $emotion): ?>
                                        <div class="confidence-item">
                                            <span class="emotion-label"><?php echo ucfirst($emotion['name']); ?></span>
                                            <div class="confidence-bar-container">
                                                <div class="confidence-bar" id="confidence-<?php echo $emotion['name']; ?>" style="width: 0%"></div>
                                            </div>
                                            <span class="confidence-value" id="confidence-value-<?php echo $emotion['name']; ?>">0%</span>
                                        </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Text Input Detection -->
                        <div class="method-panel" id="text-panel">
                            <div class="text-input-container">
                                <textarea id="emotion-text" placeholder="Describe how you're feeling right now..."></textarea>
                                <button id="analyze-text" class="btn primary-btn">
                                    <i class="fas fa-search"></i> Analyze Text
                                </button>
                            </div>
                            
                            <div class="detection-result" id="text-result" style="display: none;">
                                <h3>Detected Emotion: <span id="text-detected-emotion">-</span></h3>
                                <p>Based on your text, we've detected that you might be feeling <span id="text-emotion-description">-</span>.</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="emotion-actions" style="display: none;">
                    <h3>What would you like to do?</h3>
                    
                    <div class="target-emotion">
                        <p>I want to feel:</p>
                        <div class="emotion-buttons">
                            <?php foreach ($emotions as $emotion): ?>
                            <button class="emotion-button" data-emotion="<?php echo $emotion['name']; ?>">
                                <?php echo ucfirst($emotion['name']); ?>
                            </button>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    
                    <button id="get-recommendations" class="btn primary-btn" disabled>
                        <i class="fas fa-lightbulb"></i> Get Recommendations
                    </button>
                </div>
            </div>
        </div>
    </main>
    
    <?php include '../includes/footer.php'; ?>
    
    <script src="<?php echo BASE_URL; ?>/assets/js/emotion-detector.js"></script>
</body>
</html>
