<?php
/**
 * User API
 * 
 * Handles user-related requests like login, registration, and profile management.
 */

// Initialize session and include required files
session_start();
require_once '../config/config.php';
require_once '../config/database.php';
require_once '../classes/Database.php';
require_once '../classes/User.php';

// Get the action from the request
$action = isset($_GET['action']) ? $_GET['action'] : '';

switch ($action) {
    case 'login':
        // Process login
        login();
        break;
        
    case 'register':
        // Process registration
        register();
        break;
        
    case 'logout':
        // Process logout
        logout();
        break;
        
    case 'profile':
        // Get user profile
        getProfile();
        break;
        
    case 'update-profile':
        // Update user profile
        updateProfile();
        break;
        
    case 'emotion-history':
        // Get user's emotion history
        getEmotionHistory();
        break;
        
    case 'recommendation-history':
        // Get user's recommendation history
        getRecommendationHistory();
        break;
        
    default:
        // Invalid action
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => 'Invalid action'
        ]);
        break;
}

/**
 * Process login
 */
function login() {
    // Check if form is submitted
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        header('Location: ../pages/login.php');
        exit;
    }
    
    // Get form data
    $email = isset($_POST['email']) ? trim($_POST['email']) : '';
    $password = isset($_POST['password']) ? $_POST['password'] : '';
    
    // Validate form data
    if (empty($email) || empty($password)) {
        $_SESSION['error'] = 'Please fill in all fields';
        header('Location: ../pages/login.php');
        exit;
    }
    
    // Attempt login
    $userObj = new User();
    $user = $userObj->login($email, $password);
    
    if ($user) {
        // Login successful
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        
        // Redirect to home page
        header('Location: ../index.php');
        exit;
    } else {
        // Login failed
        $_SESSION['error'] = 'Invalid email or password';
        header('Location: ../pages/login.php');
        exit;
    }
}

/**
 * Process registration
 */
function register() {
    // Check if form is submitted
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        header('Location: ../pages/register.php');
        exit;
    }
    
    // Get form data
    $username = isset($_POST['username']) ? trim($_POST['username']) : '';
    $email = isset($_POST['email']) ? trim($_POST['email']) : '';
    $password = isset($_POST['password']) ? $_POST['password'] : '';
    $confirmPassword = isset($_POST['confirm_password']) ? $_POST['confirm_password'] : '';
    
    // Validate form data
    if (empty($username) || empty($email) || empty($password) || empty($confirmPassword)) {
        $_SESSION['error'] = 'Please fill in all fields';
        header('Location: ../pages/register.php');
        exit;
    }
    
    if ($password !== $confirmPassword) {
        $_SESSION['error'] = 'Passwords do not match';
        header('Location: ../pages/register.php');
        exit;
    }
    
    // Check if email already exists
    $userObj = new User();
    if ($userObj->findUserByEmail($email)) {
        $_SESSION['error'] = 'Email already exists';
        header('Location: ../pages/register.php');
        exit;
    }
    
    // Check if username already exists
    if ($userObj->findUserByUsername($username)) {
        $_SESSION['error'] = 'Username already exists';
        header('Location: ../pages/register.php');
        exit;
    }
    
    // Register user
    $userData = [
        'username' => $username,
        'email' => $email,
        'password' => $password
    ];
    
    if ($userObj->register($userData)) {
        // Registration successful
        $_SESSION['success'] = 'Registration successful. You can now log in.';
        header('Location: ../pages/login.php');
        exit;
    } else {
        // Registration failed
        $_SESSION['error'] = 'Registration failed. Please try again.';
        header('Location: ../pages/register.php');
        exit;
    }
}

/**
 * Process logout
 */
function logout() {
    // Unset all session variables
    $_SESSION = [];
    
    // Destroy the session
    session_destroy();
    
    // Redirect to login page
    header('Location: ../pages/login.php');
    exit;
}

/**
 * Get user profile
 */
function getProfile() {
    // Check if user is logged in
    if (!isset($_SESSION['user_id'])) {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => 'User not authenticated'
        ]);
        exit;
    }
    
    $userId = $_SESSION['user_id'];
    $userObj = new User();
    $user = $userObj->getUserById($userId);
    
    if ($user) {
        // Remove sensitive data
        unset($user['password']);
        
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'user' => $user
        ]);
    } else {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => 'User not found'
        ]);
    }
}

/**
 * Update user profile
 */
function updateProfile() {
    // Check if user is logged in
    if (!isset($_SESSION['user_id'])) {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => 'User not authenticated'
        ]);
        exit;
    }
    
    // Check if form is submitted
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        header('Location: ../pages/profile.php');
        exit;
    }
    
    // TODO: Implement profile update logic
    
    header('Location: ../pages/profile.php');
    exit;
}

/**
 * Get user's emotion history
 */
function getEmotionHistory() {
    // Check if user is logged in
    if (!isset($_SESSION['user_id'])) {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => 'User not authenticated'
        ]);
        exit;
    }
    
    $userId = $_SESSION['user_id'];
    $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 10;
    
    $userObj = new User();
    $history = $userObj->getUserEmotionHistory($userId, $limit);
    
    header('Content-Type: application/json');
    echo json_encode([
        'success' => true,
        'history' => $history
    ]);
}

/**
 * Get user's recommendation history
 */
function getRecommendationHistory() {
    // Check if user is logged in
    if (!isset($_SESSION['user_id'])) {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => 'User not authenticated'
        ]);
        exit;
    }
    
    $userId = $_SESSION['user_id'];
    $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 10;
    
    $userObj = new User();
    $history = $userObj->getUserRecommendationHistory($userId, $limit);
    
    header('Content-Type: application/json');
    echo json_encode([
        'success' => true,
        'history' => $history
    ]);
}
?>
