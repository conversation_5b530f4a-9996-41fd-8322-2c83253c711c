<?php
// Recommendations page
session_start();
require_once '../config/config.php';
require_once '../config/database.php';
require_once '../classes/Database.php';
require_once '../classes/User.php';
require_once '../classes/Emotion.php';
require_once '../classes/Recommendation.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

$userId = $_SESSION['user_id'];
$userObj = new User();
$user = $userObj->getUserById($userId);

$emotionObj = new Emotion();
$recommendationObj = new Recommendation();

// Get parameters
$type = isset($_GET['type']) ? $_GET['type'] : 'movie';
$emotionName = isset($_GET['emotion']) ? $_GET['emotion'] : '';
$targetEmotionName = isset($_GET['target']) ? $_GET['target'] : '';

// If no emotion specified, get user's latest emotion
if (empty($emotionName)) {
    $latestEmotion = $emotionObj->getUserLatestEmotion($userId);
    if ($latestEmotion) {
        $emotionName = $latestEmotion['emotion_name'];
    } else {
        $emotionName = 'neutral'; // Default
    }
}

// If no target emotion specified, get complementary emotion
if (empty($targetEmotionName)) {
    $targetEmotionName = $emotionObj->getComplementaryEmotion($emotionName);
}

// Get emotion data
$emotion = $emotionObj->getEmotionByName($emotionName);
$targetEmotion = $emotionObj->getEmotionByName($targetEmotionName);

// Get recommendations
$recommendations = $recommendationObj->getRecommendationsByEmotion($targetEmotionName, $type, 10);

// If no recommendations in database, try API
if (empty($recommendations) && $type == 'movie') {
    $recommendations = $recommendationObj->getMovieRecommendationsFromAPI($targetEmotionName, 10);
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Recommendations - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/style.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/recommendations.css">
    <script src="https://kit.fontawesome.com/7331d8ff14.js"></script>
</head>
<body>
    <?php include '../includes/header.php'; ?>
    
    <main>
        <?php include '../includes/sidebar.php'; ?>
        
        <div class="container">
            <div class="recommendations-container">
                <div class="recommendations-header">
                    <h1>Your Personalized Recommendations</h1>
                    <div class="emotion-info">
                        <p>
                            Based on your <span class="emotion-label"><?php echo ucfirst($emotionName); ?></span> mood, 
                            we recommend content to help you feel <span class="emotion-label"><?php echo ucfirst($targetEmotionName); ?></span>.
                        </p>
                    </div>
                    
                    <div class="recommendation-filters">
                        <div class="filter-buttons">
                            <a href="?emotion=<?php echo $emotionName; ?>&target=<?php echo $targetEmotionName; ?>&type=movie" 
                               class="filter-button <?php echo $type == 'movie' ? 'active' : ''; ?>">
                                <i class="fas fa-film"></i> Movies
                            </a>
                            <a href="?emotion=<?php echo $emotionName; ?>&target=<?php echo $targetEmotionName; ?>&type=music" 
                               class="filter-button <?php echo $type == 'music' ? 'active' : ''; ?>">
                                <i class="fas fa-music"></i> Music
                            </a>
                            <a href="?emotion=<?php echo $emotionName; ?>&target=<?php echo $targetEmotionName; ?>&type=meal" 
                               class="filter-button <?php echo $type == 'meal' ? 'active' : ''; ?>">
                                <i class="fas fa-utensils"></i> Meals
                            </a>
                            <a href="?emotion=<?php echo $emotionName; ?>&target=<?php echo $targetEmotionName; ?>&type=joke" 
                               class="filter-button <?php echo $type == 'joke' ? 'active' : ''; ?>">
                                <i class="fas fa-laugh"></i> Jokes
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="recommendations-grid">
                    <?php if (empty($recommendations)): ?>
                        <div class="no-recommendations">
                            <i class="fas fa-search"></i>
                            <h3>No recommendations found</h3>
                            <p>We couldn't find any <?php echo $type; ?> recommendations for your current mood.</p>
                            <a href="emotion-detection.php" class="btn primary-btn">Try Another Mood</a>
                        </div>
                    <?php else: ?>
                        <?php foreach ($recommendations as $recommendation): ?>
                            <div class="recommendation-card">
                                <?php if (!empty($recommendation['image_url'])): ?>
                                    <div class="recommendation-image">
                                        <img src="<?php echo $recommendation['image_url']; ?>" alt="<?php echo $recommendation['title']; ?>">
                                    </div>
                                <?php endif; ?>
                                
                                <div class="recommendation-content">
                                    <h3 class="recommendation-title"><?php echo $recommendation['title']; ?></h3>
                                    
                                    <?php if (!empty($recommendation['description'])): ?>
                                        <p class="recommendation-description"><?php echo substr($recommendation['description'], 0, 100); ?>...</p>
                                    <?php endif; ?>
                                    
                                    <div class="recommendation-actions">
                                        <?php if ($type == 'movie'): ?>
                                            <a href="https://www.themoviedb.org/movie/<?php echo $recommendation['external_id']; ?>" target="_blank" class="btn secondary-btn">
                                                <i class="fas fa-info-circle"></i> Details
                                            </a>
                                        <?php elseif ($type == 'music'): ?>
                                            <a href="#" class="btn secondary-btn play-music" data-id="<?php echo $recommendation['id']; ?>">
                                                <i class="fas fa-play"></i> Play
                                            </a>
                                        <?php endif; ?>
                                        
                                        <div class="rating" data-id="<?php echo $recommendation['id']; ?>">
                                            <i class="far fa-star" data-rating="1"></i>
                                            <i class="far fa-star" data-rating="2"></i>
                                            <i class="far fa-star" data-rating="3"></i>
                                            <i class="far fa-star" data-rating="4"></i>
                                            <i class="far fa-star" data-rating="5"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </main>
    
    <?php include '../includes/footer.php'; ?>
    
    <script src="<?php echo BASE_URL; ?>/assets/js/recommendations.js"></script>
</body>
</html>
