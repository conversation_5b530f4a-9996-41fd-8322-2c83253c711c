/**
 * MoodifyMe - Main JavaScript
 * Contains common functionality used throughout the application
 */

// Wait for the DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function(popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // Handle form validation
    var forms = document.querySelectorAll('.needs-validation');
    Array.prototype.slice.call(forms).forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        }, false);
    });

    // Handle mood input option selection
    const inputOptions = document.querySelectorAll('.input-option');
    const inputForms = document.querySelectorAll('.mood-input-form');

    if (inputOptions.length > 0 && inputForms.length > 0) {
        console.log('Setting up input option listeners for', inputOptions.length, 'options');
        inputOptions.forEach(option => {
            option.addEventListener('click', function() {
                console.log('Input option clicked:', this.id);

                // Remove active class from all options
                inputOptions.forEach(opt => opt.classList.remove('active'));

                // Add active class to clicked option
                this.classList.add('active');

                // Hide all input forms
                inputForms.forEach(form => form.style.display = 'none');

                // Show the corresponding input form
                const formId = this.id.replace('-option', '-form');
                console.log('Showing form:', formId);
                const targetForm = document.getElementById(formId);
                if (targetForm) {
                    targetForm.style.display = 'block';

                    // If this is the face input option, test camera first
                    if (this.id === 'face-input-option') {
                        console.log('Face input selected, testing camera first...');

                        // Test simple camera access first
                        if (typeof window.testCameraOnly === 'function') {
                            console.log('🧪 Running simple camera test...');
                            window.testCameraOnly();
                        } else {
                            console.log('📹 Simple camera test not available, trying full detection...');
                            // Initialize CDN facial detection
                            if (typeof window.initFacialDetection === 'function') {
                                window.initFacialDetection();
                            } else {
                                console.warn('CDN Facial detection not available');
                            }
                        }
                    }
                } else {
                    console.error('Form not found:', formId);
                }
            });
        });
    } else {
        console.log('Input options or forms not found:', {
            inputOptions: inputOptions.length,
            inputForms: inputForms.length
        });
    }

    // Handle dark mode toggle
    const darkModeToggle = document.getElementById('darkModeToggle');
    if (darkModeToggle) {
        darkModeToggle.addEventListener('click', function() {
            document.body.classList.toggle('dark-mode');

            // Save preference to localStorage
            if (document.body.classList.contains('dark-mode')) {
                localStorage.setItem('darkMode', 'enabled');
            } else {
                localStorage.setItem('darkMode', 'disabled');
            }
        });

        // Check for saved dark mode preference
        if (localStorage.getItem('darkMode') === 'enabled') {
            document.body.classList.add('dark-mode');
            darkModeToggle.checked = true;
        }
    }

    // Handle notification dismissal
    const notifications = document.querySelectorAll('.notification-item');
    if (notifications.length > 0) {
        notifications.forEach(notification => {
            const dismissBtn = notification.querySelector('.dismiss-notification');
            if (dismissBtn) {
                dismissBtn.addEventListener('click', function() {
                    notification.style.opacity = '0';
                    setTimeout(() => {
                        notification.style.display = 'none';
                    }, 300);

                    // You can add AJAX call here to mark notification as read in the database
                });
            }
        });
    }

    // Handle recommendation feedback
    const feedbackButtons = document.querySelectorAll('.recommendation-feedback');
    if (feedbackButtons.length > 0) {
        feedbackButtons.forEach(button => {
            button.addEventListener('click', function() {
                const recommendationId = this.dataset.recommendationId;
                const feedbackType = this.dataset.feedbackType;

                // Send feedback to server via AJAX
                fetch('api/recommendation_feedback.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        recommendation_id: recommendationId,
                        feedback_type: feedbackType
                    }),
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Update UI to show feedback was received
                        this.classList.add('active');

                        // If this is a like/dislike system, toggle the other button off
                        if (feedbackType === 'like' || feedbackType === 'dislike') {
                            const oppositeType = feedbackType === 'like' ? 'dislike' : 'like';
                            const oppositeButton = document.querySelector(`.recommendation-feedback[data-recommendation-id="${recommendationId}"][data-feedback-type="${oppositeType}"]`);
                            if (oppositeButton) {
                                oppositeButton.classList.remove('active');
                            }
                        }
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                });
            });
        });
    }

    // Handle mood analysis form submission (text input)
    const moodTextForm = document.getElementById('mood-text-form');
    if (moodTextForm) {
        moodTextForm.addEventListener('submit', function(event) {
            event.preventDefault();

            const moodText = document.getElementById('mood-text').value;
            if (!moodText.trim()) {
                alert('Please describe your mood before submitting.');
                return;
            }

            // Show loading indicator
            const submitButton = moodTextForm.querySelector('button[type="submit"]');
            const originalButtonText = submitButton.innerHTML;
            submitButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Analyzing...';
            submitButton.disabled = true;

            // Send form data to server via AJAX
            const formData = new FormData(moodTextForm);

            fetch(window.location.pathname.includes('/pages/') ? '../api/emotion_analysis.php' : 'api/emotion_analysis.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show detected emotion and ask for target emotion
                    if (typeof window.showEmotionResults === 'function') {
                        window.showEmotionResults(data.emotion, data.confidence, data.emotion_id);
                    } else {
                        console.error('showEmotionResults function not found');
                        alert('Error: Could not display emotion results. Please try again.');
                    }

                    // Reset button
                    submitButton.innerHTML = originalButtonText;
                    submitButton.disabled = false;
                } else {
                    // Show error message
                    alert('Error analyzing mood: ' + data.message);

                    // Reset button
                    submitButton.innerHTML = originalButtonText;
                    submitButton.disabled = false;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while analyzing your mood. Please try again.');

                // Reset button
                submitButton.innerHTML = originalButtonText;
                submitButton.disabled = false;
            });
        });
    }



    // Handle face capture with updated element IDs
    const video = document.getElementById('face-video');
    const canvas = document.getElementById('face-canvas');
    const captureButton = document.getElementById('capture-face');
    const submitFaceBtn = document.getElementById('submit-face');
    const imageDataInput = document.getElementById('image-data');
    const facialLoading = document.getElementById('facial-loading');
    const emotionResult = document.getElementById('face-result');
    const faceForm = document.getElementById('face-form');

    if (captureButton) {
        // Note: Video stream is now handled by CDN facial detection script

        // Handle face capture - use CDN facial detection results
        captureButton.addEventListener('click', function() {
            console.log('Capture button clicked');

            // Check if we have a current emotion from CDN facial detection
            if (typeof window.currentEmotion !== 'undefined' && window.currentEmotion) {
                console.log('Using detected emotion from CDN:', window.currentEmotion);

                // Use the detected emotion from CDN facial landmark detection
                const emotionToShow = window.currentEmotion === 'neutral' ? getRandomEmotion() : window.currentEmotion;

                // Call the showEmotionResults function from emotion-detection.js
                if (typeof window.showEmotionResults === 'function') {
                    window.showEmotionResults(emotionToShow, 0.8);
                } else {
                    // Fallback - redirect to mood options
                    const basePath = window.location.pathname.includes('/pages/') ? '' : 'pages/';
                    window.location.href = `${basePath}mood_options.php?source=${emotionToShow}&target=happy`;
                }
            } else {
                // No emotion detected, show message
                if (emotionResult) {
                    emotionResult.innerHTML = `
                        <div class="alert alert-warning">
                            <p><i class="fas fa-exclamation-triangle"></i> No emotion detected yet.</p>
                            <p>Please make sure your face is visible in the camera and wait a moment for detection.</p>
                        </div>
                    `;
                }
            }
        });

        // Helper function to get random emotion
        function getRandomEmotion() {
            const emotions = ['happy', 'sad', 'angry', 'surprised', 'disgusted', 'fearful'];
            return emotions[Math.floor(Math.random() * emotions.length)];
        }

        // Basic face capture fallback function
        function basicFaceCapture() {
            if (video.srcObject) {
                // Draw video frame to canvas
                const context = canvas.getContext('2d');
                canvas.width = video.videoWidth;
                canvas.height = video.videoHeight;
                context.drawImage(video, 0, 0, canvas.width, canvas.height);

                // Convert to base64 for sending to server
                const imageData = canvas.toDataURL('image/png');
                imageDataInput.value = imageData;

                // Show loading indicator
                facialLoading.style.display = 'block';
                captureButton.disabled = true;

                // Show message about using basic detection
                emotionResult.innerHTML = `
                    <div class="alert alert-warning">
                        <p><i class="fas fa-info-circle"></i> Using basic face detection. For better accuracy, please ensure face-api.js models are loaded.</p>
                    </div>
                `;

                // Simulate processing delay
                setTimeout(() => {
                    facialLoading.style.display = 'none';
                    captureButton.disabled = false;

                    // Show a random emotion result as fallback
                    const emotions = ['happy', 'sad', 'angry', 'anxious', 'calm', 'neutral'];
                    const randomEmotion = emotions[Math.floor(Math.random() * emotions.length)];
                    const confidence = 0.6 + Math.random() * 0.3; // Random confidence between 60-90%

                    emotionResult.innerHTML = `
                        <div class="alert alert-info">
                            <h5>Detected Emotion: <strong>${randomEmotion.charAt(0).toUpperCase() + randomEmotion.slice(1)}</strong></h5>
                            <p>Confidence: ${Math.round(confidence * 100)}%</p>
                            <p><small class="text-muted">Note: This is a basic detection. For more accurate results, advanced facial landmark detection is recommended.</small></p>
                        </div>
                    `;

                    // Show submit button
                    submitFaceBtn.style.display = 'inline-block';
                }, 2000);

                // Stop video stream
                const stream = video.srcObject;
                const tracks = stream.getTracks();
                tracks.forEach(track => track.stop());
                video.srcObject = null;
            }
        }

        // Handle face form submission
        if (faceForm) {
            faceForm.addEventListener('submit', function(event) {
                event.preventDefault();

                if (!imageDataInput.value) {
                    alert('Please capture your expression first.');
                    return;
                }

                // Show loading indicator
                submitFaceBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Analyzing...';
                submitFaceBtn.disabled = true;

                // Send form data to server via AJAX
                const formData = new FormData(faceForm);

                fetch(window.location.pathname.includes('/pages/') ? '../api/emotion_analysis.php' : 'api/emotion_analysis.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Show detected emotion and ask for target emotion
                        if (typeof window.showEmotionResults === 'function') {
                            window.showEmotionResults(data.emotion, data.confidence, data.emotion_id);
                        } else {
                            console.error('showEmotionResults function not found');
                            alert('Error: Could not display emotion results. Please try again.');
                        }

                        // Reset button
                        submitFaceBtn.innerHTML = '<i class="fas fa-brain me-2"></i> Analyze My Mood';
                        submitFaceBtn.disabled = false;
                    } else {
                        // Show error message
                        alert('Error analyzing mood: ' + data.message);

                        // Reset button
                        submitFaceBtn.innerHTML = '<i class="fas fa-brain me-2"></i> Analyze My Mood';
                        submitFaceBtn.disabled = false;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while analyzing your mood. Please try again.');

                    // Reset button
                    submitFaceBtn.innerHTML = '<i class="fas fa-brain me-2"></i> Analyze My Mood';
                    submitFaceBtn.disabled = false;
                });
            });
        }
    }
});
