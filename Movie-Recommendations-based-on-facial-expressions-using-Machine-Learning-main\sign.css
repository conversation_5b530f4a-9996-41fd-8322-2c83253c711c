@import url('https://fonts.googleapis.com/css2?family=Fredoka+One&display=swap');

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  font-family: sans-serif;
}

.blob {
    position: absolute;
    top: 50%;
    left: 50%;
    height: 25vw;
    border-radius: 50%;
    translate: -50% -50%;
    aspect-ratio: 1;
    background: linear-gradient(to right, #8360c3, #2ebf91);
    animation: rotate 25s infinite;
    filter: blur(10vw);
}
body {
  background-color: #000;
}

h1 {
  margin-bottom: 8%;
  text-align: center;
  font-family: "Fredoka One", cursive;
  color: #000;
  letter-spacing: 0.1em;
}

p {
  margin-top: 5%;
  margin-bottom: 5%;
  width: 100%;
  text-align: center;
  border-bottom: 1px solid #000;
  line-height: 0.1em;
}

p span {
  background:grey;
  padding:0 10px;
}

input {
  margin-bottom: 3%;
}

input:last-of-type {
  margin-bottom: 0;
}

input, button {
  padding: 3%;
  width: 100%;
}

.login-container {
  background-color: grey;
  padding: 7%;
  box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2);
  /* horizontal align */
  width: 40vw;
  margin-left: 30vw;
  /* vertical align */
  height: 70vh;
  margin-top: 15vh;
}

#submit, #create-acct-btn {
  background-color: black;
  color: grey;
  border: none;
  margin-top: 5%;
}

button:hover {
  cursor: pointer;
  opacity: 0.8;
  transition: 0.3s;
}

#sign-up {
  border: none;
}

#create-acct {
  display: none;
}

#return-btn {
  background: none;
  color: white;
  text-decoration: underline;
  border: none;
  margin-top: 3%;
}
