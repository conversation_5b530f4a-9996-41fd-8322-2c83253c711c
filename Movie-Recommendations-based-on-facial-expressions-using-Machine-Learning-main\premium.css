@font-face {
  font-family: font;
  src: url(fonts/main.otf);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  user-select: none;
  font-family: "font";
  cursor: url(icons/cursor.png), auto;
}

.blob {
  position: absolute;
  top: 50%;
  left: 50%;
  height: 25vw;
  border-radius: 50%;
  translate: -50% -50%;
  aspect-ratio: 1;
  background: linear-gradient(to right, #8360c3, #2ebf91);
  animation: rotate 25s infinite;
  filter: blur(10vw);
}

p {
  text-align: center;
  font-size: large;
  margin-bottom: 50px;
}

body {
  color: white;
  background-color: black;
  height: 100vh;
  overflow: hidden;
}

section {
  margin-top: 25px;
  position: absolute;
  top: 50%;
  left: 50%;
  margin-right: -50%;
  transform: translate(-50%, -50%);
}

p {
  font-size: x-large;
  margin-bottom: 20px;
}

.grid {
  border: 2px solid white;
  border-radius: 5px;
  padding: 10px;
  margin: 2px;
  text-decoration: none;
}

ul {
  text-align: left;
}

li {
  list-style: none;
}

li a {
  text-decoration: none;
  color: red;
  font-weight: 700;
}

.grid {
  height: 22.5%;
  background: transparent;
  border-radius: 1.95vw;
  backdrop-filter: blur(35px);
  display: flex;
  align-items: center;
  justify-content: space-evenly;
  box-shadow: 10 10 5px white;
  margin: 10px;
}

nav {
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  padding: 1.25%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.icon {
  width: 2.75vw;
  height: 2.75vw;
}

nav .ul1 .li1 {
  list-style: none;
  display: inline-block;
  margin-left: 2.5vw;
  margin-right: 0.75vw;
}

.a1 {
  text-decoration: none;
  position: relative;
  padding-bottom: 0.75vw;
  transition: 0.25s;
  color: white;
}

.a1::before {
  content: '';
  position: absolute;
  width: 100%;
  height: 0.25vw;
  border-radius: 1vw;
  background-image: radial-gradient(circle farthest-corner at 92.3% 71.5%, rgba(83, 138, 214, 1) 0%, rgba(134, 231, 214, 1) 90%);
  bottom: 0;
  left: 0;
  transform-origin: right;
  transform: scaleX(0);
  background-size: 200%;
  animation: flow 2.5s ease-in-out infinite;
  transition: transform 0.25s ease-in-out;
}

.a1:hover::before {
  transform-origin: left;
  transform: scaleX(1);
}