<?php
// Main entry point for the application
session_start();
require_once 'config/config.php';
require_once 'config/database.php';
require_once 'classes/Database.php';
require_once 'classes/User.php';

// Check if user is logged in
$loggedIn = isset($_SESSION['user_id']);

// Redirect to login page if not logged in
if (!$loggedIn && !in_array($_SERVER['REQUEST_URI'], ['/login.php', '/register.php'])) {
    header('Location: pages/login.php');
    exit;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Emotion-Based Recommendation System</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <script src="https://kit.fontawesome.com/7331d8ff14.js"></script>
</head>
<body>
    <?php include 'includes/header.php'; ?>
    
    <main>
        <?php include 'includes/sidebar.php'; ?>
        
        <div class="container">
            <div class="hero-section">
                <h1>Welcome to Your Emotion-Based Recommendation System</h1>
                <p>Discover content tailored to your emotional state and preferences</p>
                
                <div class="action-buttons">
                    <a href="pages/emotion-detection.php" class="btn primary-btn">
                        <i class="fas fa-smile"></i> Detect My Mood
                    </a>
                    <a href="pages/recommendations.php" class="btn secondary-btn">
                        <i class="fas fa-film"></i> View Recommendations
                    </a>
                </div>
            </div>
            
            <div class="features">
                <div class="feature-card">
                    <i class="fas fa-camera feature-icon"></i>
                    <h3>Facial Expression Analysis</h3>
                    <p>Our system analyzes your facial expressions to detect your current emotional state.</p>
                </div>
                
                <div class="feature-card">
                    <i class="fas fa-film feature-icon"></i>
                    <h3>Movie Recommendations</h3>
                    <p>Get personalized movie suggestions based on your current mood or desired emotional state.</p>
                </div>
                
                <div class="feature-card">
                    <i class="fas fa-music feature-icon"></i>
                    <h3>Music Suggestions</h3>
                    <p>Discover music that resonates with your emotions or helps you achieve your desired mood.</p>
                </div>
                
                <div class="feature-card">
                    <i class="fas fa-utensils feature-icon"></i>
                    <h3>Meal Recommendations</h3>
                    <p>Find meal ideas that complement your emotional state and enhance your well-being.</p>
                </div>
            </div>
        </div>
    </main>
    
    <?php include 'includes/footer.php'; ?>
    
    <script src="assets/js/main.js"></script>
</body>
</html>
